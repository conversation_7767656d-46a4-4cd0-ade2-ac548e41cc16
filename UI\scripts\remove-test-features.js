/**
 * 删除测试功能脚本
 * 运行此脚本可以完全移除测试用户选择功能
 * 
 * 使用方法：
 * node scripts/remove-test-features.js
 */

const fs = require('fs');
const path = require('path');

// 需要删除的文件列表
const filesToDelete = [
  'utils/testUserService.js',
  'components/TestUserSelector.vue',
  'docs/测试用户功能说明.md',
  'test-user-function.html',
  'scripts/remove-test-features.js' // 删除自己
];

// 需要修改的文件和对应的修改操作
const filesToModify = [
  {
    file: 'pages/video/index.vue',
    modifications: [
      {
        // 移除测试组件导入
        search: /\/\/ 测试组件 - 可以轻松删除\nimport TestUserSelector from "@\/components\/TestUserSelector\.vue";\n/,
        replace: ''
      },
      {
        // 移除组件注册
        search: /,\s*\/\/ 测试组件 - 可以轻松删除\s*TestUserSelector/,
        replace: ''
      },
      {
        // 移除测试用户选择器显示控制
        search: /\/\/ 测试用户选择器显示控制 - 可以轻松删除\s*showTestUserSelector: false,?\s*/,
        replace: ''
      },
      {
        // 移除测试用户选择组件
        search: /<!-- 测试用户选择组件 -->\s*<TestUserSelector[^>]*\/>\s*/,
        replace: ''
      },
      {
        // 移除初始化测试用户选择器调用
        search: /\/\/ 初始化测试用户选择器 - 可以轻松删除\s*this\.initTestUserSelector\(\);\s*/,
        replace: ''
      },
      {
        // 移除测试相关方法
        search: /\/\*\*\s*\* 初始化测试用户选择器 - 可以轻松删除[\s\S]*?\},\s*\/\*\*\s*\* 测试用户变更事件处理 - 可以轻松删除[\s\S]*?\},/,
        replace: ''
      }
    ]
  }
];

console.log('开始删除测试功能...\n');

// 删除文件
filesToDelete.forEach(file => {
  const filePath = path.join(__dirname, '..', file);
  
  if (fs.existsSync(filePath)) {
    try {
      fs.unlinkSync(filePath);
      console.log(`✓ 已删除文件: ${file}`);
    } catch (error) {
      console.error(`✗ 删除文件失败: ${file} - ${error.message}`);
    }
  } else {
    console.log(`- 文件不存在: ${file}`);
  }
});

console.log('\n开始修改文件...\n');

// 修改文件
filesToModify.forEach(({ file, modifications }) => {
  const filePath = path.join(__dirname, '..', file);
  
  if (!fs.existsSync(filePath)) {
    console.log(`- 文件不存在: ${file}`);
    return;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    modifications.forEach((mod, index) => {
      if (mod.search.test(content)) {
        content = content.replace(mod.search, mod.replace);
        modified = true;
        console.log(`  ✓ 应用修改 ${index + 1}/${modifications.length}`);
      } else {
        console.log(`  - 修改 ${index + 1}/${modifications.length} 未找到匹配内容`);
      }
    });

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✓ 已修改文件: ${file}`);
    } else {
      console.log(`- 文件无需修改: ${file}`);
    }
  } catch (error) {
    console.error(`✗ 修改文件失败: ${file} - ${error.message}`);
  }
});

console.log('\n测试功能删除完成！');
console.log('\n删除的功能包括：');
console.log('- 测试用户数据服务');
console.log('- 测试用户选择组件');
console.log('- 视频页面中的测试功能集成');
console.log('- 相关文档和测试文件');
console.log('\n原有的业务逻辑完全保持不变。');
