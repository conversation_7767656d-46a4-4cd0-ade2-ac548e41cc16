# 测试用户功能说明

## 功能概述

在测试环境中，为了方便测试视频观看和答题功能，我们在视频页面添加了测试用户选择功能。该功能允许开发者快速切换不同的测试用户，模拟真实的用户登录状态。

## 使用方法

### 1. 访问测试功能

1. 在测试环境中打开视频页面（需要有 `batchId` 参数）
2. 页面右上角会显示一个 🔧 图标（开发工具按钮）
3. 点击该图标展开开发工具面板
4. 点击"测试用户"按钮打开测试用户选择弹窗

### 2. 选择测试用户

1. 在弹窗中可以看到所有可用的测试用户列表
2. 每个用户显示：
   - 头像和昵称
   - 用户ID和员工ID
   - 最后登录时间
   - 审核状态（所有测试用户都是已审核状态）
3. 点击任意用户即可切换到该用户
4. 当前选中的用户会高亮显示

### 3. 测试功能

切换用户后，系统会：
- 将用户信息写入本地存储
- 生成模拟的用户token
- 重新加载页面数据
- 用户可以正常观看视频和进行答题

### 4. 清除测试状态

- 点击"清除测试登录"按钮可以清除当前测试用户状态
- 点击"清除缓存"按钮可以清除所有缓存数据

## 测试用户列表

系统提供了以下测试用户：

1. **小明同学** (ID: 1) - emp_001
2. **阳光女孩** (ID: 2) - emp_001  
3. **健康达人** (ID: 3) - emp_002
4. **学习小能手** (ID: 4) - emp_002
5. **运动爱好者** (ID: 5) - emp_003
6. **张小明** (ID: 9) - emp_001
7. **李小红** (ID: 10) - emp_001
8. **王小刚** (ID: 11) - emp_001
9. **赵小美** (ID: 12) - emp_001
10. **刘小强** (ID: 13) - emp_001

所有测试用户都已通过审核，可以正常使用视频观看和答题功能。

## 环境检测

测试功能只在以下情况下显示：
- 配置文件中 `debugMode` 为 `true`
- 或者访问地址包含 `localhost`、`test`、`dev` 等关键词

在生产环境中，该功能会自动隐藏。

## 技术实现

### 文件结构
- `utils/testUserService.js` - 测试用户数据和服务
- `pages/video/index.vue` - 视频页面（已添加测试功能）

### 存储机制
- 使用与真实登录相同的存储键名：`wechatUserInfo`、`wechatUserToken`
- 添加测试标识：`isTestLogin`、`testUserId`
- 完全兼容现有的认证检查机制

### 模拟登录
- 生成格式为 `test_token_{userId}_{timestamp}` 的模拟token
- 用户信息格式与真实登录完全一致
- 支持所有现有的用户相关功能

## 注意事项

1. 该功能仅用于开发和测试环境
2. 测试用户数据基于真实的数据库记录
3. 切换用户后会重新加载页面数据，可能需要等待几秒钟
4. 建议在测试完成后清除测试登录状态

## 故障排除

### 测试功能不显示
- 检查是否在测试环境中
- 确认 `app-config.js` 中 `debugMode` 为 `true`
- 刷新页面重试

### 切换用户失败
- 检查浏览器控制台是否有错误信息
- 尝试清除缓存后重新操作
- 确认网络连接正常

### 视频或答题功能异常
- 确认选择的测试用户状态正常
- 检查 `batchId` 参数是否正确
- 尝试切换到其他测试用户
