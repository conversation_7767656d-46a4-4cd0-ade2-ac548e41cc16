import{_ as s,a as t,I as a,T as e,r as i,b as n,c as o,w as l,aF as c,i as d,aG as r,a9 as u,o as g,h,j as f,n as m,t as _,k as b,l as w}from"./index-BbEPSs4u.js";import{f as A,s as U}from"./sysuser.09c1XOSb.js";import{p}from"./permission-mixin.0SnNiQ4H.js";const y=s({name:"AdminSettings",mixins:[p],data:()=>({loading:!1,enableUserAudit:!1,switchLoading:!1,managerId:""}),onLoad(){const s=t.getUserType();["manager","agent","admin","super_admin"].includes(s)?(this.initManagerInfo(),this.loadAuditConfig()):a({title:"权限不足",content:"只有管理员和超管可以访问设置页面",showCancel:!1,success:()=>{e()}})},methods:{initManagerInfo(){try{const s=t.getLoginInfo();this.managerId=s.id||s.userId||s.user_id||"",console.log("当前管理员信息:",s),console.log("管理员ID:",this.managerId)}catch(s){console.error("获取管理员信息失败:",s),this.$u.toast("获取管理员信息失败"),e()}},async loadAuditConfig(){try{this.loading=!0,console.log("开始获取审核配置...");const s=await A();console.log("获取审核配置响应:",s),s.success?(this.enableUserAudit=s.data||!1,console.log("审核配置加载成功:",this.enableUserAudit)):(console.error("获取审核配置失败:",s.msg),this.$u.toast(s.msg||"获取配置失败"),this.enableUserAudit=!1)}catch(s){console.error("获取审核配置失败:",s),this.$u.toast("获取配置失败"),this.enableUserAudit=!1}finally{this.loading=!1}},toggleSwitch(){if(this.switchLoading)return;const s=!this.enableUserAudit;this.handleUserAuditChange(s)},async handleUserAuditChange(s){try{this.switchLoading=!0;const t=void 0!==s?s:this.enableUserAudit;console.log("开始更新审核配置:",t);const a=await U(t);if(console.log("更新审核配置响应:",a),a.success){this.enableUserAudit=t;const s=t?"开启":"关闭";this.$u.toast(`已${s}用户审核功能`),console.log("审核配置更新成功:",t)}else console.error("更新审核配置失败:",a.msg),this.$u.toast(a.msg||"更新失败，请稍后重试")}catch(t){console.error("更新审核配置失败:",t),this.$u.toast("网络错误，请稍后重试")}finally{this.switchLoading=!1}}}},[["render",function(s,t,a,e,A,U){const p=i(n("u-navbar"),c),y=w,I=d,x=i(n("u-notice-bar"),r),C=i(n("u-loadmore"),u);return g(),o(I,{class:"container"},{default:l((()=>[h(p,{title:"设置",autoBack:!0,placeholder:!0}),h(I,{class:"settings-section"},{default:l((()=>[h(I,{class:"setting-card"},{default:l((()=>[h(I,{class:"setting-header"},{default:l((()=>[h(y,{class:"setting-title"},{default:l((()=>[f("用户审核")])),_:1})])),_:1}),h(I,{class:"setting-content"},{default:l((()=>[h(I,{class:"setting-info"},{default:l((()=>[h(y,{class:"info-main"},{default:l((()=>[f("开启后，新用户注册需要人工审核")])),_:1}),h(y,{class:"info-sub"},{default:l((()=>[f("关闭后，新用户将自动通过审核")])),_:1})])),_:1}),h(I,{class:"setting-control"},{default:l((()=>[h(I,{class:m(["custom-switch",{"switch-on":A.enableUserAudit}]),onClick:U.toggleSwitch},{default:l((()=>[h(I,{class:"switch-handle"})])),_:1},8,["class","onClick"]),h(y,{class:"switch-text"},{default:l((()=>[f(_(A.enableUserAudit?"开启":"关闭"),1)])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),h(I,{class:"impact-section"},{default:l((()=>[h(x,{text:"配置变更将立即生效，影响您下属的所有员工。请谨慎操作。",type:"warning",scrollable:!1})])),_:1}),A.loading?(g(),o(C,{key:0,status:"loading",loadingText:"加载中..."})):b("",!0)])),_:1})}],["__scopeId","data-v-22e3f110"]]);export{y as default};
