<template>
  <view class="error-test-page">
    <view class="header">
      <text class="title">错误处理机制测试</text>
      <text class="subtitle">测试业务错误和系统错误的处理逻辑</text>
    </view>

    <view class="test-section">
      <view class="section-title">业务错误测试（HTTP 200 + code 500）</view>
      <view class="test-buttons">
        <button
          class="test-btn business-error"
          @click="testBusinessException"
          :loading="loading.businessException"
        >
          测试业务异常
        </button>
      </view>
    </view>

    <view class="test-section">
      <view class="section-title">系统错误测试（HTTP 500）</view>
      <view class="test-buttons">
        <button
          class="test-btn system-error"
          @click="testSystemException"
          :loading="loading.systemException"
        >
          测试系统异常
        </button>
      </view>
    </view>

    <view class="test-section">
      <view class="section-title">授权错误测试（HTTP 401）</view>
      <view class="test-buttons">
        <button
          class="test-btn auth-error"
          @click="testAuthorizationException"
          :loading="loading.authorizationException"
        >
          测试授权异常
        </button>
      </view>
    </view>

    <view class="test-section">
      <view class="section-title">正常响应测试</view>
      <view class="test-buttons">
        <button
          class="test-btn success"
          @click="testSuccessResponse"
          :loading="loading.successResponse"
        >
          测试正常响应
        </button>

        <button
          class="test-btn info"
          @click="getAllErrorCodes"
          :loading="loading.getAllErrorCodes"
        >
          获取所有错误码
        </button>
      </view>
    </view>

    <view class="test-section">
      <view class="section-title">跳过自动错误处理测试</view>
      <view class="test-buttons">
        <button
          class="test-btn skip-handler"
          @click="testSkipBusinessErrorHandler"
          :loading="loading.skipHandler"
        >
          跳过业务错误处理
        </button>
      </view>
    </view>

    <view class="results-section" v-if="testResults.length > 0">
      <view class="section-title">测试结果</view>
      <view class="results-list">
        <view
          v-for="(result, index) in testResults"
          :key="index"
          class="result-item"
          :class="result.type"
        >
          <view class="result-header">
            <text class="result-title">{{ result.title }}</text>
            <text class="result-time">{{ result.time }}</text>
          </view>
          <view class="result-content">{{ result.content }}</view>
        </view>
      </view>

      <button class="clear-btn" @click="clearResults">清空结果</button>
    </view>
  </view>
</template>

<script>
import { get } from "@/utils/request.js";
import { requestWithoutBusinessErrorHandler } from "@/utils/request.js";
import { showBusinessSuccess } from "@/utils/business-error-handler.js";

export default {
  name: "ErrorTestPage",
  data() {
    return {
      loading: {
        businessException: false,
        systemException: false,
        authorizationException: false,
        successResponse: false,
        getAllErrorCodes: false,
        skipHandler: false,
      },
      testResults: [],
    };
  },

  methods: {
    // 添加测试结果
    addResult(title, content, type = "info") {
      const now = new Date();
      const time = `${now.getHours().toString().padStart(2, "0")}:${now
        .getMinutes()
        .toString()
        .padStart(2, "0")}:${now.getSeconds().toString().padStart(2, "0")}`;

      this.testResults.unshift({
        title,
        content,
        type,
        time,
      });

      // 限制结果数量
      if (this.testResults.length > 20) {
        this.testResults = this.testResults.slice(0, 20);
      }
    },

    // 清空结果
    clearResults() {
      this.testResults = [];
    },

    // 测试业务异常
    async testBusinessException() {
      this.loading.businessException = true;
      try {
        const response = await get("/test/ErrorTest/business-exception");
        this.addResult(
          "业务异常测试",
          `响应: ${JSON.stringify(response)}`,
          "business-error"
        );
      } catch (error) {
        this.addResult("业务异常测试", `异常: ${error.message}`, "error");
      } finally {
        this.loading.businessException = false;
      }
    },

    // 测试系统异常
    async testSystemException() {
      this.loading.systemException = true;
      try {
        const response = await get("/test/ErrorTest/system-exception");
        this.addResult(
          "系统异常测试",
          `响应: ${JSON.stringify(response)}`,
          "system-error"
        );
      } catch (error) {
        this.addResult("系统异常测试", `异常: ${error.message}`, "error");
      } finally {
        this.loading.systemException = false;
      }
    },

    // 测试授权异常
    async testAuthorizationException() {
      this.loading.authorizationException = true;
      try {
        const response = await get("/test/ErrorTest/authorization-exception");
        this.addResult(
          "授权异常测试",
          `响应: ${JSON.stringify(response)}`,
          "auth-error"
        );
      } catch (error) {
        this.addResult("授权异常测试", `异常: ${error.message}`, "error");
      } finally {
        this.loading.authorizationException = false;
      }
    },

    // 测试正常响应
    async testSuccessResponse() {
      this.loading.successResponse = true;
      try {
        const response = await get("/test/ErrorTest/success");
        this.addResult(
          "正常响应测试",
          `响应: ${JSON.stringify(response)}`,
          "success"
        );
        showBusinessSuccess("正常响应测试成功！");
      } catch (error) {
        this.addResult("正常响应测试", `异常: ${error.message}`, "error");
      } finally {
        this.loading.successResponse = false;
      }
    },

    // 获取所有错误码
    async getAllErrorCodes() {
      this.loading.getAllErrorCodes = true;
      try {
        const response = await get("/test/ErrorTest/error-codes");
        this.addResult(
          "获取错误码信息",
          `响应: ${JSON.stringify(response)}`,
          "info"
        );
      } catch (error) {
        this.addResult("获取错误码信息", `异常: ${error.message}`, "error");
      } finally {
        this.loading.getAllErrorCodes = false;
      }
    },

    // 测试跳过业务错误处理
    async testSkipBusinessErrorHandler() {
      this.loading.skipHandler = true;
      try {
        const response = await requestWithoutBusinessErrorHandler.get(
          "/test/ErrorTest/business-exception"
        );

        // 手动处理业务错误
        if (!response.success && response.code === 500) {
          this.addResult(
            "跳过自动错误处理测试",
            `手动处理业务错误: ${response.msg}`,
            "skip-handler"
          );

          // 手动显示自定义错误提示
          uni.showModal({
            title: "自定义错误处理",
            content: `检测到业务错误: ${response.msg}`,
            showCancel: false,
          });
        } else {
          this.addResult(
            "跳过自动错误处理测试",
            `响应: ${JSON.stringify(response)}`,
            "skip-handler"
          );
        }
      } catch (error) {
        this.addResult(
          "跳过自动错误处理测试",
          `异常: ${error.message}`,
          "error"
        );
      } finally {
        this.loading.skipHandler = false;
      }
    },
  },

  onLoad() {
    // 页面加载时的初始化
    this.addResult(
      "页面初始化",
      "错误处理测试页面已加载，可以开始测试各种错误场景",
      "info"
    );
  },
};
</script>

<style lang="scss" scoped>
.error-test-page {
  padding: 32rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 48rpx;

  .title {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 16rpx;
  }

  .subtitle {
    display: block;
    font-size: 28rpx;
    color: #666;
  }
}

.test-section {
  margin-bottom: 48rpx;
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #eee;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.test-btn {
  padding: 24rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;

  &.business-error {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;

    &:active {
      background: linear-gradient(135deg, #ee5a52, #dc4c41);
    }
  }

  &.system-error {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;

    &:active {
      background: linear-gradient(135deg, #c82333, #a71e2a);
    }
  }

  &.auth-error {
    background: linear-gradient(135deg, #fd7e14, #e8590c);
    color: white;

    &:active {
      background: linear-gradient(135deg, #e8590c, #d63384);
    }
  }

  &.param-error {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #333;

    &:active {
      background: linear-gradient(135deg, #e0a800, #d39e00);
    }
  }

  &.custom-error {
    background: linear-gradient(135deg, #6f42c1, #5a32a3);
    color: white;

    &:active {
      background: linear-gradient(135deg, #5a32a3, #4c2a85);
    }
  }

  &.success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;

    &:active {
      background: linear-gradient(135deg, #1e7e34, #155724);
    }
  }

  &.info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;

    &:active {
      background: linear-gradient(135deg, #138496, #0f6674);
    }
  }

  &.skip-handler {
    background: linear-gradient(135deg, #6c757d, #545b62);
    color: white;

    &:active {
      background: linear-gradient(135deg, #545b62, #3d4142);
    }
  }
}

.results-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.results-list {
  max-height: 800rpx;
  overflow-y: auto;
}

.result-item {
  padding: 24rpx;
  margin-bottom: 16rpx;
  border-radius: 12rpx;
  border-left: 8rpx solid;

  &.business-error {
    background: #fff5f5;
    border-left-color: #ff6b6b;
  }

  &.system-error {
    background: #fff1f1;
    border-left-color: #dc3545;
  }

  &.auth-error {
    background: #fff8f0;
    border-left-color: #fd7e14;
  }

  &.param-error {
    background: #fffbf0;
    border-left-color: #ffc107;
  }

  &.custom-error {
    background: #f8f5ff;
    border-left-color: #6f42c1;
  }

  &.success {
    background: #f0fff4;
    border-left-color: #28a745;
  }

  &.info {
    background: #f0f9ff;
    border-left-color: #17a2b8;
  }

  &.skip-handler {
    background: #f8f9fa;
    border-left-color: #6c757d;
  }

  &.error {
    background: #fff0f0;
    border-left-color: #dc3545;
  }
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.result-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.result-time {
  font-size: 24rpx;
  color: #999;
}

.result-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  word-break: break-all;
}

.clear-btn {
  width: 100%;
  padding: 24rpx;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  margin-top: 24rpx;

  &:active {
    background: #545b62;
  }
}
</style>
