import{q as o,u as r,E as s}from"./index-BbEPSs4u.js";async function i(i,t={}){const{loadingTitle:a="加载中...",errorTitle:n="加载失败",showLoading:e=!0,showError:w=!0}=t;try{e&&o({title:a});const s=await i();if(e&&r(),s&&s.success)return s;throw new Error((null==s?void 0:s.msg)||"API调用失败")}catch(h){throw e&&r(),w&&s(n),h}}const t={silent:{showLoading:!1,showError:!1},quick:{loadingTitle:"加载中...",showLoading:!0,showError:!0},important:{loadingTitle:"处理中，请稍候...",errorTitle:"操作失败，请重试",showLoading:!0,showError:!0},background:{showLoading:!1,showError:!1,retry:!0,maxRetries:2}};export{t as E,i as a};
