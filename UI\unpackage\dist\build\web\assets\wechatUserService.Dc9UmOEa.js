function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["assets/utils-wechatWebAuth.BbxsB9sM.js","assets/index-BbEPSs4u.js","assets/index-D34YhECH.css","assets/video-user.C-TCm_6q.js"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
import{m as e,L as t,J as r,K as n,a5 as a,M as s}from"./index-BbEPSs4u.js";import{w as o}from"./video-user.C-TCm_6q.js";function c(t){return e.get("/Wechat/authorize",t)}function i(e={}){const r=Math.random().toString(36).substring(2,15)+Date.now().toString(36),n={timestamp:Date.now(),random:Math.random().toString(36).substring(2,11),...e};try{return t(`wechat_state_${r}`,n),r}catch(a){return console.error("构建状态参数失败:",a),""}}function u(e){try{if(!e)return null;const a=r(`wechat_state_${e}`);if(a)return n(`wechat_state_${e}`),a;try{const t=atob(e);return JSON.parse(t)}catch(t){return console.warn("无法解析状态参数，可能是新旧版本不兼容:",t),null}}catch(a){return console.error("解析状态参数失败:",a),null}}const l="wechatUserInfo",d="wechatUserToken";const g=new class{async wechatLogin(e){try{const t=await o(e);if(t.success&&t.data){const e=t.data.userToken||t.data.token;if(void 0!==t.data.auditStatus&&1!==t.data.auditStatus){const e=0===t.data.auditStatus?"待审核":"已拒绝";throw new Error(`用户审核状态：${e}，无法使用业务功能`)}return this.saveUserInfo(t.data.userInfo),e&&this.saveUserToken(e),{success:!0,message:"微信登录成功",data:{...t.data,token:e}}}throw new Error(t.message||"微信登录失败")}catch(t){return console.error("微信登录失败:",t),{success:!1,message:t.message||"微信登录失败，请重试"}}}async tryWechatAutoLogin(e){try{return await this.tryWechatWebAuth(e)}catch(t){throw console.error("微信自动登录失败:",t),t}}async tryWechatWebAuth(e){try{const t=(await a((()=>import("./utils-wechatWebAuth.BbxsB9sM.js")),__vite__mapDeps([0,1,2,3]))).default;if(!t.isH5Environment())throw new Error("当前环境不支持微信网页授权");const r={batchId:e.batchId?parseInt(e.batchId):null,returnUrl:e.returnUrl||"/pages/index/index"};return await t.startAuth({extraState:r,inviterId:e.sharerId||e.employeeId||null}),{success:!0,message:"正在跳转到微信授权页面..."}}catch(t){throw console.error("微信网页授权失败:",t),t}}async wechatWebLogin(t){try{const{Code:a,State:s,InviterId:o}=t,c=await(n={Code:a,State:s,InviterId:o||""},e.get("/Wechat/callback",n));if(c.success&&c.data){const e=(r=c.data.userInfo||c.data.user)?{id:r.id||r.userId||"",nickname:r.nickname||r.nickName||"微信用户",avatar:r.avatar||r.headimgurl||"",openid:r.openid||"",unionid:r.unionid||"",gender:r.gender||0,city:r.city||"",province:r.province||"",country:r.country||"",language:r.language||"zh_CN"}:null,t=c.data.userToken||c.data.token;return 1===c.data.auditStatus?(console.log("用户已审核通过，缓存用户信息"),this.saveUserInfo(e),t&&this.saveUserToken(t)):(console.log("用户未审核通过，不缓存用户信息，状态:",c.data.auditStatus),this.clearUserInfo(),this.clearUserToken()),{success:!0,message:"微信网页登录成功",data:{userInfo:e,token:t,auditStatus:c.data.auditStatus,isNewUser:c.data.isNewUser}}}throw new Error(c.msg||c.message||"微信网页登录失败")}catch(a){return console.error("微信网页登录失败:",a),{success:!1,message:a.message||"微信网页登录失败，请重试"}}var r,n}saveUserInfo(e){try{t(l,e)}catch(r){console.error("Error saving wechat user info:",r)}}saveUserToken(e){try{t(d,e)}catch(r){console.error("Error saving wechat user token:",r)}}getUserInfo(){try{return r(l)||null}catch(e){return console.error("Error getting wechat user info:",e),null}}getUserToken(){try{return r(d)||null}catch(e){return console.error("Error getting wechat user token:",e),null}}isLoggedIn(){const e=this.getUserInfo(),t=this.getUserToken();return!(!e||!t)}getUserId(){const e=this.getUserInfo();return(null==e?void 0:e.id)??null}getNickname(){const e=this.getUserInfo();return(null==e?void 0:e.nickname)??null}getAvatar(){const e=this.getUserInfo();return(null==e?void 0:e.avatar)??null}getOpenId(){const e=this.getUserInfo();return(null==e?void 0:e.openId)??null}getEmployeeId(){const e=this.getUserInfo();return(null==e?void 0:e.employeeId)??null}logout(){try{return n(l),n(d),console.log("微信用户已登出"),!0}catch(e){return console.error("Error during wechat user logout:",e),!1}}getDisplayInfo(){const e=this.getUserInfo();return e?{id:e.id,nickname:e.nickname||"微信用户",avatar:e.avatar||"/static/logo.png",openId:e.openId,employeeId:e.employeeId,userType:"wechat_user",createTime:e.createTime,lastLogin:e.lastLogin}:{nickname:"未登录",avatar:"/static/logo.png",userType:"guest"}}redirectToLogin(e=""){const t=e?`/pages/user-login/index?returnUrl=${encodeURIComponent(e)}`:"/pages/user-login/index";s({url:t})}requireAuth(){return!this.isLoggedIn()&&(console.log("微信用户未登录，需要认证"),!0)}clearUserInfo(){try{n(l),console.log("已清除微信用户信息")}catch(e){console.error("清除用户信息失败:",e)}}clearUserToken(){try{n(d),console.log("已清除微信用户Token")}catch(e){console.error("清除用户Token失败:",e)}}clearAll(){this.clearUserInfo(),this.clearUserToken(),console.log("已清除所有微信用户数据")}},h=e=>g.wechatWebLogin(e);export{h as a,i as b,c as g,u as p,g as w};
