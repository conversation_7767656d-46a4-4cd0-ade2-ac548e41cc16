<template>
  <view class="test-user-selector">
    <!-- 悬浮测试按钮 -->
    <view class="test-floating-btn" @click="showModal = true" v-if="isTestEnvironment">
      <text class="test-btn-icon">👤</text>
      <text class="test-btn-text">测试</text>
    </view>

    <!-- 测试用户选择弹窗 -->
    <view v-if="showModal" class="test-user-modal">
      <view class="test-user-modal-content">
        <view class="test-user-header">
          <text class="test-user-title">选择测试用户</text>
          <text class="test-user-close" @click="showModal = false">✕</text>
        </view>
        
        <view class="current-user-info" v-if="currentTestUser">
          <text class="current-user-label">当前测试用户：</text>
          <view class="current-user-card">
            <image :src="currentTestUser.avatar" class="current-user-avatar" mode="aspectFill"></image>
            <view class="current-user-details">
              <text class="current-user-name">{{ currentTestUser.nickname }}</text>
              <text class="current-user-id">ID: {{ currentTestUser.id }}</text>
            </view>
          </view>
        </view>

        <view class="test-user-list">
          <view 
            v-for="user in testUsers" 
            :key="user.id" 
            class="test-user-item"
            :class="{ 'active': currentTestUser && currentTestUser.id === user.id }"
            @click="selectTestUser(user)"
          >
            <image :src="user.avatar" class="test-user-avatar" mode="aspectFill"></image>
            <view class="test-user-info">
              <text class="test-user-name">{{ user.nickname }}</text>
              <text class="test-user-details">ID: {{ user.id }} | 员工: {{ user.employeeId }}</text>
              <text class="test-user-login">最后登录: {{ user.lastLogin }}</text>
            </view>
            <view class="test-user-status">
              <text class="status-badge">已审核</text>
            </view>
          </view>
        </view>

        <view class="test-user-actions">
          <button class="test-action-btn clear-test-btn" @click="clearTestLogin">清除测试登录</button>
          <button class="test-action-btn cancel-btn" @click="showModal = false">取消</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import testUserService from '@/utils/testUserService.js'

export default {
  name: 'TestUserSelector',
  
  data() {
    return {
      showModal: false,
      testUsers: [],
      currentTestUser: null,
      isTestEnvironment: false
    }
  },

  mounted() {
    this.initTestEnvironment()
  },

  methods: {
    /**
     * 初始化测试环境
     */
    initTestEnvironment() {
      try {
        // 检查是否为测试环境
        this.isTestEnvironment = testUserService.isTestEnvironment()
        
        if (this.isTestEnvironment) {
          // 加载测试用户列表
          this.testUsers = testUserService.getAllTestUsers()
          
          // 检查当前是否有测试用户登录
          if (testUserService.isTestLogin()) {
            const testUserId = testUserService.getCurrentTestUserId()
            this.currentTestUser = testUserService.getTestUserById(testUserId)
          }
          
          console.log('测试用户选择器已初始化，用户数量:', this.testUsers.length)
        }
      } catch (error) {
        console.error('初始化测试环境失败:', error)
        this.isTestEnvironment = false
      }
    },

    /**
     * 选择测试用户
     */
    async selectTestUser(user) {
      try {
        uni.showLoading({ title: '切换用户中...', mask: true })

        // 模拟用户登录
        const result = await testUserService.simulateUserLogin(user.id)
        
        if (result.success) {
          this.currentTestUser = user
          this.showModal = false
          
          uni.hideLoading()
          uni.showToast({
            title: `已切换到 ${user.nickname}`,
            icon: 'success'
          })

          // 通知父组件用户已变更
          this.$emit('user-changed', user)
        } else {
          uni.hideLoading()
          uni.showToast({
            title: result.message || '切换用户失败',
            icon: 'error'
          })
        }
      } catch (error) {
        uni.hideLoading()
        console.error('选择测试用户失败:', error)
        uni.showToast({
          title: '切换用户失败',
          icon: 'error'
        })
      }
    },

    /**
     * 清除测试登录
     */
    clearTestLogin() {
      uni.showModal({
        title: '清除测试登录',
        content: '确定要清除当前测试用户登录状态吗？',
        success: (res) => {
          if (res.confirm) {
            try {
              testUserService.clearTestLogin()
              this.currentTestUser = null
              this.showModal = false
              
              uni.showToast({
                title: '测试登录已清除',
                icon: 'success'
              })

              // 通知父组件用户已变更
              this.$emit('user-changed', null)
            } catch (error) {
              console.error('清除测试登录失败:', error)
              uni.showToast({
                title: '清除失败',
                icon: 'error'
              })
            }
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.test-user-selector {
  position: relative;
}

/* 悬浮测试按钮 */
.test-floating-btn {
  position: fixed;
  top: 120rpx;
  right: 30rpx;
  width: 100rpx;
  height: 100rpx;
  background: #52c41a;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
  z-index: 9998;
  cursor: pointer;
}

.test-floating-btn:hover {
  background: #73d13d;
  transform: scale(1.05);
}

.test-btn-icon {
  font-size: 32rpx;
  margin-bottom: 4rpx;
}

.test-btn-text {
  font-size: 20rpx;
  color: white;
  font-weight: bold;
}

/* 测试用户弹窗样式 */
.test-user-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.test-user-modal-content {
  width: 90%;
  max-width: 800rpx;
  max-height: 80vh;
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.test-user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.test-user-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-user-close {
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
  padding: 10rpx;
}

.test-user-close:hover {
  color: #666;
}

.current-user-info {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.current-user-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
  display: block;
}

.current-user-card {
  display: flex;
  align-items: center;
}

.current-user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.current-user-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.current-user-id {
  font-size: 24rpx;
  color: #666;
}

.test-user-list {
  flex: 1;
  overflow-y: auto;
  max-height: 500rpx;
}

.test-user-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 15rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.test-user-item:hover {
  background: #e6f7ff;
  transform: translateY(-2rpx);
}

.test-user-item.active {
  background: #e6f7ff;
  border-color: #1890ff;
}

.test-user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.test-user-info {
  flex: 1;
}

.test-user-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.test-user-details {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.test-user-login {
  font-size: 22rpx;
  color: #999;
}

.test-user-status {
  display: flex;
  align-items: center;
}

.status-badge {
  padding: 8rpx 16rpx;
  background: #52c41a;
  color: white;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.test-user-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #f0f0f0;
}

.test-action-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-test-btn {
  background: #ff4d4f;
  color: white;
}

.clear-test-btn:hover {
  background: #ff7875;
}

.cancel-btn {
  background: #f0f0f0;
  color: #666;
}

.cancel-btn:hover {
  background: #d9d9d9;
}
</style>
