<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试用户功能验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        .test-result {
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .user-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .user-card {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .user-card:hover {
            background: #e6f7ff;
            border-color: #1890ff;
        }
        .user-card.active {
            background: #e6f7ff;
            border-color: #1890ff;
            font-weight: bold;
        }
        .user-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .user-details {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试用户功能验证</h1>
        
        <div class="test-section">
            <div class="test-title">1. 环境检测</div>
            <button onclick="checkEnvironment()">检测测试环境</button>
            <div id="env-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 测试用户服务</div>
            <button onclick="loadTestUsers()">加载测试用户</button>
            <button onclick="checkCurrentUser()">检查当前用户</button>
            <div id="service-result" class="test-result"></div>
            <div id="user-list" class="user-list"></div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 模拟登录测试</div>
            <button onclick="simulateLogin('1')">登录用户1</button>
            <button onclick="simulateLogin('9')">登录用户9</button>
            <button onclick="clearTestLogin()">清除测试登录</button>
            <div id="login-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 存储验证</div>
            <button onclick="checkStorage()">检查本地存储</button>
            <button onclick="clearStorage()">清除存储</button>
            <div id="storage-result" class="test-result"></div>
        </div>
    </div>

    <!-- 模拟uni对象 -->
    <script>
        // 模拟uni-app的存储API
        window.uni = {
            setStorageSync: function(key, value) {
                localStorage.setItem(key, JSON.stringify(value));
            },
            getStorageSync: function(key) {
                try {
                    const value = localStorage.getItem(key);
                    return value ? JSON.parse(value) : null;
                } catch (e) {
                    return null;
                }
            },
            removeStorageSync: function(key) {
                localStorage.removeItem(key);
            },
            clearStorageSync: function() {
                localStorage.clear();
            }
        };

        // 模拟APP_CONFIG
        window.APP_CONFIG = {
            debugMode: true
        };
    </script>

    <!-- 加载测试用户服务 -->
    <script src="./utils/testUserService.js"></script>

    <script>
        let testUserService;
        let currentTestUsers = [];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 动态导入测试用户服务
            import('./utils/testUserService.js').then(module => {
                testUserService = module.default;
                showResult('service-result', '测试用户服务已加载', 'success');
            }).catch(error => {
                showResult('service-result', '加载测试用户服务失败: ' + error.message, 'error');
            });
        });

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `test-result ${type}`;
        }

        function checkEnvironment() {
            try {
                const isTest = window.location.hostname === 'localhost' || 
                              window.location.hostname.includes('test') || 
                              window.APP_CONFIG.debugMode;
                
                showResult('env-result', 
                    `测试环境检测: ${isTest ? '是' : '否'}\n` +
                    `主机名: ${window.location.hostname}\n` +
                    `调试模式: ${window.APP_CONFIG.debugMode}`, 
                    isTest ? 'success' : 'error'
                );
            } catch (error) {
                showResult('env-result', '环境检测失败: ' + error.message, 'error');
            }
        }

        function loadTestUsers() {
            try {
                if (!testUserService) {
                    showResult('service-result', '测试用户服务未加载', 'error');
                    return;
                }

                currentTestUsers = testUserService.getAllTestUsers();
                showResult('service-result', `成功加载 ${currentTestUsers.length} 个测试用户`, 'success');
                
                // 显示用户列表
                const userListElement = document.getElementById('user-list');
                userListElement.innerHTML = '';
                
                currentTestUsers.forEach(user => {
                    const userCard = document.createElement('div');
                    userCard.className = 'user-card';
                    userCard.onclick = () => selectUser(user);
                    userCard.innerHTML = `
                        <div class="user-name">${user.nickname}</div>
                        <div class="user-details">ID: ${user.id}</div>
                        <div class="user-details">员工: ${user.employeeId}</div>
                    `;
                    userListElement.appendChild(userCard);
                });
            } catch (error) {
                showResult('service-result', '加载测试用户失败: ' + error.message, 'error');
            }
        }

        function selectUser(user) {
            // 高亮选中的用户
            document.querySelectorAll('.user-card').forEach(card => {
                card.classList.remove('active');
            });
            event.target.closest('.user-card').classList.add('active');
            
            // 模拟登录
            simulateLogin(user.id);
        }

        async function simulateLogin(userId) {
            try {
                if (!testUserService) {
                    showResult('login-result', '测试用户服务未加载', 'error');
                    return;
                }

                const result = await testUserService.simulateUserLogin(userId);
                
                if (result.success) {
                    showResult('login-result', 
                        `登录成功!\n` +
                        `用户: ${result.data.userInfo.nickname}\n` +
                        `ID: ${result.data.userInfo.id}\n` +
                        `Token: ${result.data.token.substring(0, 20)}...`, 
                        'success'
                    );
                } else {
                    showResult('login-result', '登录失败: ' + result.message, 'error');
                }
            } catch (error) {
                showResult('login-result', '登录异常: ' + error.message, 'error');
            }
        }

        function checkCurrentUser() {
            try {
                if (!testUserService) {
                    showResult('service-result', '测试用户服务未加载', 'error');
                    return;
                }

                const isTestLogin = testUserService.isTestLogin();
                const currentUserId = testUserService.getCurrentTestUserId();
                const currentUser = currentUserId ? testUserService.getTestUserById(currentUserId) : null;
                
                showResult('service-result', 
                    `测试登录状态: ${isTestLogin ? '是' : '否'}\n` +
                    `当前用户ID: ${currentUserId || '无'}\n` +
                    `当前用户: ${currentUser ? currentUser.nickname : '无'}`, 
                    isTestLogin ? 'success' : 'info'
                );
            } catch (error) {
                showResult('service-result', '检查当前用户失败: ' + error.message, 'error');
            }
        }

        function clearTestLogin() {
            try {
                if (!testUserService) {
                    showResult('login-result', '测试用户服务未加载', 'error');
                    return;
                }

                const success = testUserService.clearTestLogin();
                showResult('login-result', 
                    success ? '测试登录状态已清除' : '清除失败', 
                    success ? 'success' : 'error'
                );
                
                // 清除用户卡片的选中状态
                document.querySelectorAll('.user-card').forEach(card => {
                    card.classList.remove('active');
                });
            } catch (error) {
                showResult('login-result', '清除测试登录失败: ' + error.message, 'error');
            }
        }

        function checkStorage() {
            try {
                const userInfo = uni.getStorageSync('wechatUserInfo');
                const userToken = uni.getStorageSync('wechatUserToken');
                const isTestLogin = uni.getStorageSync('isTestLogin');
                const testUserId = uni.getStorageSync('testUserId');
                
                showResult('storage-result', 
                    `用户信息: ${userInfo ? JSON.stringify(userInfo, null, 2) : '无'}\n` +
                    `用户Token: ${userToken ? userToken.substring(0, 30) + '...' : '无'}\n` +
                    `测试登录: ${isTestLogin}\n` +
                    `测试用户ID: ${testUserId || '无'}`, 
                    'info'
                );
            } catch (error) {
                showResult('storage-result', '检查存储失败: ' + error.message, 'error');
            }
        }

        function clearStorage() {
            try {
                uni.clearStorageSync();
                showResult('storage-result', '所有存储数据已清除', 'success');
            } catch (error) {
                showResult('storage-result', '清除存储失败: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
