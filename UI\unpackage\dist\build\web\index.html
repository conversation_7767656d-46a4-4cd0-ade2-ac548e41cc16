<!DOCTYPE html>
<html lang="en">

<head>
  <link rel="stylesheet" href="/assets/uni.6b9e09c4.css">

  <meta charset="UTF-8" />
  <script>
    var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
      CSS.supports('top: constant(a)'))
    document.write(
      '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
      (coverSupport ? ', viewport-fit=cover' : '') + '" />')
  </script>
  <title>NewUI</title>
  <!-- 应用配置文件 - 可在发布后直接修改 -->
  <script src="/static/config/app-config.js?v=1.0.1"></script>

  <!--preload-links-->
  <!--app-context-->
  <script type="module" crossorigin src="/assets/index-BbEPSs4u.js"></script>
  <link rel="stylesheet" crossorigin href="/assets/index-D34YhECH.css">
</head>

<body>
  <div id="app"><!--app-html--></div>
</body>

</html>