/**
 * 缓存管理工具
 * 提供内存缓存和本地存储缓存功能
 */

// 内存缓存
const memoryCache = new Map();

// 缓存配置
const CACHE_CONFIG = {
  // 默认过期时间（毫秒）
  DEFAULT_TTL: 5 * 60 * 1000, // 5分钟

  // 不同类型数据的过期时间
  TTL: {
    VIDEO_LIST: 3 * 60 * 1000,      // 视频列表 3分钟
    VIDEO_DETAIL: 10 * 60 * 1000,   // 视频详情 10分钟
    BATCH_LIST: 2 * 60 * 1000,      // 批次列表 2分钟
    BATCH_DETAIL: 5 * 60 * 1000,    // 批次详情 5分钟
    STATISTICS: 1 * 60 * 1000,      // 统计数据 1分钟
    USER_PROGRESS: 30 * 1000,       // 用户进度 30秒
  },

  // 最大缓存条目数
  MAX_CACHE_SIZE: 100
};

/**
 * 生成缓存键
 * @param {string} type - 缓存类型
 * @param {string|Object} identifier - 标识符
 * @returns {string} 缓存键
 */
function generateCacheKey (type, identifier) {
  if (typeof identifier === 'object') {
    identifier = JSON.stringify(identifier);
  }
  return `${type}:${identifier}`;
}

/**
 * 内存缓存管理
 */
export class MemoryCache {
  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {*} value - 缓存值
   * @param {number} ttl - 过期时间（毫秒）
   */
  static set (key, value, ttl = CACHE_CONFIG.DEFAULT_TTL) {
    const expireTime = Date.now() + ttl;

    // 检查缓存大小限制
    if (memoryCache.size >= CACHE_CONFIG.MAX_CACHE_SIZE) {
      // 删除最旧的缓存项
      const firstKey = memoryCache.keys().next().value;
      memoryCache.delete(firstKey);
    }

    memoryCache.set(key, {
      value,
      expireTime
    });
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   * @returns {*} 缓存值，如果不存在或已过期返回null
   */
  static get (key) {
    const cached = memoryCache.get(key);

    if (!cached) {
      return null;
    }

    // 检查是否过期
    if (Date.now() > cached.expireTime) {
      memoryCache.delete(key);
      return null;
    }

    return cached.value;
  }

  /**
   * 删除缓存
   * @param {string} key - 缓存键
   */
  static delete (key) {
    memoryCache.delete(key);
  }

  /**
   * 清空所有缓存
   */
  static clear () {
    memoryCache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  static getStats () {
    return {
      size: memoryCache.size,
      maxSize: CACHE_CONFIG.MAX_CACHE_SIZE,
      keys: Array.from(memoryCache.keys())
    };
  }
}

/**
 * 本地存储缓存管理
 */
export class StorageCache {
  /**
   * 设置缓存到本地存储
   * @param {string} key - 缓存键
   * @param {*} value - 缓存值
   * @param {number} ttl - 过期时间（毫秒）
   */
  static set (key, value, ttl = CACHE_CONFIG.DEFAULT_TTL) {
    try {
      const expireTime = Date.now() + ttl;
      const cacheData = {
        value,
        expireTime
      };

      uni.setStorageSync(`cache_${key}`, JSON.stringify(cacheData));
    } catch (error) {
      // 静默处理缓存设置失败
    }
  }

  /**
   * 从本地存储获取缓存
   * @param {string} key - 缓存键
   * @returns {*} 缓存值，如果不存在或已过期返回null
   */
  static get (key) {
    try {
      const cacheDataStr = uni.getStorageSync(`cache_${key}`);

      if (!cacheDataStr) {
        return null;
      }

      const cacheData = JSON.parse(cacheDataStr);

      // 检查是否过期
      if (Date.now() > cacheData.expireTime) {
        uni.removeStorageSync(`cache_${key}`);
        return null;
      }

      return cacheData.value;
    } catch (error) {
      return null;
    }
  }

  /**
   * 删除本地存储缓存
   * @param {string} key - 缓存键
   */
  static delete (key) {
    try {
      uni.removeStorageSync(`cache_${key}`);
    } catch (error) {
      // 静默处理缓存删除失败
    }
  }

  /**
   * 清空所有本地缓存
   */
  static clear () {
    try {
      const storageInfo = uni.getStorageInfoSync();
      const cacheKeys = storageInfo.keys.filter(key => key.startsWith('cache_'));

      cacheKeys.forEach(key => {
        uni.removeStorageSync(key);
      });
    } catch (error) {
      // 静默处理缓存清空失败
    }
  }
}

/**
 * 智能缓存管理器
 * 结合内存缓存和本地存储缓存
 */
export class SmartCache {
  /**
   * 设置缓存
   * @param {string} type - 缓存类型
   * @param {string|Object} identifier - 标识符
   * @param {*} value - 缓存值
   * @param {Object} options - 选项
   */
  static set (type, identifier, value, options = {}) {
    const {
      useMemory = true,
      useStorage = false,
      ttl = CACHE_CONFIG.TTL[type] || CACHE_CONFIG.DEFAULT_TTL
    } = options;

    const key = generateCacheKey(type, identifier);

    if (useMemory) {
      MemoryCache.set(key, value, ttl);
    }

    if (useStorage) {
      StorageCache.set(key, value, ttl);
    }
  }

  /**
   * 获取缓存
   * @param {string} type - 缓存类型
   * @param {string|Object} identifier - 标识符
   * @param {Object} options - 选项
   * @returns {*} 缓存值
   */
  static get (type, identifier, options = {}) {
    const {
      useMemory = true,
      useStorage = false
    } = options;

    const key = generateCacheKey(type, identifier);

    // 优先从内存缓存获取
    if (useMemory) {
      const memoryValue = MemoryCache.get(key);
      if (memoryValue !== null) {
        return memoryValue;
      }
    }

    // 从本地存储获取
    if (useStorage) {
      const storageValue = StorageCache.get(key);
      if (storageValue !== null) {
        // 如果从本地存储获取到数据，同时设置到内存缓存
        if (useMemory) {
          MemoryCache.set(key, storageValue, CACHE_CONFIG.TTL[type] || CACHE_CONFIG.DEFAULT_TTL);
        }
        return storageValue;
      }
    }

    return null;
  }

  /**
   * 删除缓存
   * @param {string} type - 缓存类型
   * @param {string|Object} identifier - 标识符
   */
  static delete (type, identifier) {
    const key = generateCacheKey(type, identifier);
    MemoryCache.delete(key);
    StorageCache.delete(key);
  }

  /**
   * 清空指定类型的缓存
   * @param {string} type - 缓存类型
   */
  static clearType (type) {
    const prefix = `${type}:`;

    // 清空内存缓存
    const memoryKeys = Array.from(memoryCache.keys());
    memoryKeys.forEach(key => {
      if (key.startsWith(prefix)) {
        MemoryCache.delete(key);
      }
    });

    // 清空本地存储缓存
    try {
      const storageInfo = uni.getStorageInfoSync();
      const cacheKeys = storageInfo.keys.filter(key =>
        key.startsWith('cache_') && key.substring(6).startsWith(prefix)
      );

      cacheKeys.forEach(key => {
        uni.removeStorageSync(key);
      });
    } catch (error) {
      // 静默处理缓存清空失败
    }
  }
}

/**
 * 缓存装饰器函数
 * @param {string} type - 缓存类型
 * @param {Function} keyGenerator - 键生成函数
 * @param {Object} options - 缓存选项
 * @returns {Function} 装饰器函数
 */
export function withCache (type, keyGenerator, options = {}) {
  return function (target, propertyName, descriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args) {
      const cacheKey = keyGenerator(...args);

      // 尝试从缓存获取
      const cachedValue = SmartCache.get(type, cacheKey, options);
      if (cachedValue !== null) {
        return cachedValue;
      }

      // 执行原方法
      const result = await originalMethod.apply(this, args);

      // 设置缓存
      if (result && result.success) {
        SmartCache.set(type, cacheKey, result, options);
      }

      return result;
    };

    return descriptor;
  };
}

// 导出缓存类型常量
export const CACHE_TYPES = {
  VIDEO_LIST: 'VIDEO_LIST',
  VIDEO_DETAIL: 'VIDEO_DETAIL',
  BATCH_LIST: 'BATCH_LIST',
  BATCH_DETAIL: 'BATCH_DETAIL',
  STATISTICS: 'STATISTICS',
  USER_PROGRESS: 'USER_PROGRESS'
};
