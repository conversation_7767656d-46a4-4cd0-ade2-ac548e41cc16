import{_ as e,v as s,T as l,x as a,c as t,w as c,i as d,o,h as r,j as n,t as i,n as u,k as f,A as m,B as p,F as h,l as _,R as y,C as g}from"./index-BbEPSs4u.js";import{P as I}from"./PageHeader.0rCSQicE.js";const E=e({components:{PageHeader:I},data:()=>({sourceEmployeeId:null,sourceEmployee:null,users:[],selectedEmployeeId:null,targetEmployee:null,selectedUserIds:[],showTransferConfirmModal:!1}),computed:{availableEmployees:()=>[],filteredUsers(){return this.users.filter((e=>e.employeeId===this.sourceEmployeeId))},allSelected(){return this.filteredUsers.length>0&&this.selectedUserIds.length===this.filteredUsers.length}},onLoad(e){e.employeeId&&(this.sourceEmployeeId=parseInt(e.employeeId),this.sourceEmployee=null,this.users=[])},methods:{selectTargetEmployee(e){this.selectedEmployeeId=e.id,this.targetEmployee=e},toggleSelectUser(e){const s=this.selectedUserIds.indexOf(e.id);-1===s?this.selectedUserIds.push(e.id):this.selectedUserIds.splice(s,1)},toggleSelectAll(){this.allSelected?this.selectedUserIds=[]:this.selectedUserIds=this.filteredUsers.map((e=>e.id))},confirmTransfer(){this.selectedUserIds.length>0&&this.selectedEmployeeId&&(this.showTransferConfirmModal=!0)},closeTransferConfirmModal(){this.showTransferConfirmModal=!1},executeTransfer(){this.users.forEach((e=>{this.selectedUserIds.includes(e.id)&&(e.employeeId=this.selectedEmployeeId)})),s({title:"用户转移成功",icon:"success"}),this.closeTransferConfirmModal(),setTimeout((()=>{l()}),1500)},formatDate(e){if(!e)return"未知";const s=new Date(e);return`${s.getFullYear()}-${String(s.getMonth()+1).padStart(2,"0")}-${String(s.getDate()).padStart(2,"0")}`}}},[["render",function(e,s,l,I,E,k){const C=a("PageHeader"),U=_,T=d,b=y,v=g;return o(),t(T,{class:"container"},{default:c((()=>[r(C,{title:"用户转移"}),E.sourceEmployee?(o(),t(T,{key:0,class:"section source-employee"},{default:c((()=>[r(T,{class:"section-header"},{default:c((()=>[r(U,{class:"section-title"},{default:c((()=>[n("源员工信息")])),_:1})])),_:1}),r(T,{class:"employee-info"},{default:c((()=>[r(b,{class:"avatar",src:E.sourceEmployee.avatar||"/assets/images/avatar-placeholder.png"},null,8,["src"]),r(T,{class:"info-content"},{default:c((()=>[r(U,{class:"name"},{default:c((()=>[n(i(E.sourceEmployee.username),1)])),_:1}),r(U,{class:u(["status",{dismissed:E.sourceEmployee.dismissed}])},{default:c((()=>[n(i(E.sourceEmployee.dismissed?"已离职":"在职中"),1)])),_:1},8,["class"])])),_:1})])),_:1})])),_:1})):f("",!0),r(T,{class:"section target-employee"},{default:c((()=>[r(T,{class:"section-header"},{default:c((()=>[r(U,{class:"section-title"},{default:c((()=>[n("目标员工")])),_:1})])),_:1}),r(T,{class:"employee-selector"},{default:c((()=>[(o(!0),m(h,null,p(k.availableEmployees,(e=>(o(),t(T,{class:u(["employee-item",{selected:E.selectedEmployeeId===e.id}]),key:e.id,onClick:s=>k.selectTargetEmployee(e)},{default:c((()=>[r(b,{class:"avatar",src:e.avatar||"/assets/images/avatar-placeholder.png"},null,8,["src"]),r(U,{class:"name"},{default:c((()=>[n(i(e.username),1)])),_:2},1024),E.selectedEmployeeId===e.id?(o(),t(T,{key:0,class:"select-icon"},{default:c((()=>[r(U,{class:"iconfont icon-check"})])),_:1})):f("",!0)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1}),r(T,{class:"section users-section"},{default:c((()=>[r(T,{class:"section-header"},{default:c((()=>[r(U,{class:"section-title"},{default:c((()=>[n("待转移用户 ("+i(k.filteredUsers.length)+")",1)])),_:1}),r(T,{class:"header-actions"},{default:c((()=>[r(T,{class:"select-all",onClick:k.toggleSelectAll},{default:c((()=>[r(U,null,{default:c((()=>[n(i(k.allSelected?"取消全选":"全选"),1)])),_:1})])),_:1},8,["onClick"])])),_:1})])),_:1}),r(T,{class:"users-list"},{default:c((()=>[(o(!0),m(h,null,p(k.filteredUsers,(e=>(o(),t(T,{class:u(["user-item",{selected:E.selectedUserIds.includes(e.id)}]),key:e.id,onClick:s=>k.toggleSelectUser(e)},{default:c((()=>[r(T,{class:"select-box"},{default:c((()=>[r(T,{class:u(["checkbox",{checked:E.selectedUserIds.includes(e.id)}])},null,8,["class"])])),_:2},1024),r(b,{class:"avatar",src:e.avatar||"/assets/images/avatar-placeholder.png"},null,8,["src"]),r(T,{class:"user-info"},{default:c((()=>[r(U,{class:"name"},{default:c((()=>[n(i(e.username),1)])),_:2},1024),r(U,{class:"member-since"},{default:c((()=>[n("注册时间: "+i(k.formatDate(e.registerTime)),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128)),0===k.filteredUsers.length?(o(),t(T,{key:0,class:"empty-state"},{default:c((()=>[r(U,null,{default:c((()=>[n("没有可转移的用户")])),_:1})])),_:1})):f("",!0)])),_:1})])),_:1}),r(T,{class:"footer-actions"},{default:c((()=>[r(T,{class:"selected-count"},{default:c((()=>[n("已选择: "+i(E.selectedUserIds.length)+" 个用户",1)])),_:1}),r(v,{class:"transfer-btn",disabled:0===E.selectedUserIds.length||!E.selectedEmployeeId,onClick:k.confirmTransfer},{default:c((()=>[n(" 确认转移 ")])),_:1},8,["disabled","onClick"])])),_:1}),E.showTransferConfirmModal?(o(),t(T,{key:1,class:"modal"},{default:c((()=>[r(T,{class:"modal-content"},{default:c((()=>[r(T,{class:"modal-header"},{default:c((()=>[r(U,{class:"modal-title"},{default:c((()=>[n("确认转移")])),_:1}),r(U,{class:"close-btn",onClick:k.closeTransferConfirmModal},{default:c((()=>[n("×")])),_:1},8,["onClick"])])),_:1}),r(T,{class:"modal-body"},{default:c((()=>[r(U,{class:"modal-text"},{default:c((()=>[n(" 确定要将选中的 "+i(E.selectedUserIds.length)+" 个用户从 "+i(E.sourceEmployee?E.sourceEmployee.username:"")+" 转移至 "+i(E.targetEmployee?E.targetEmployee.username:"")+" 吗？ ",1)])),_:1}),r(T,{class:"modal-btns"},{default:c((()=>[r(v,{class:"modal-btn cancel",onClick:k.closeTransferConfirmModal},{default:c((()=>[n("取消")])),_:1},8,["onClick"]),r(v,{class:"modal-btn confirm",onClick:k.executeTransfer},{default:c((()=>[n("确认")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})])),_:1})):f("",!0)])),_:1})}],["__scopeId","data-v-2ded7222"]]);export{E as default};
