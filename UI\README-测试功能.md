# 测试用户功能

## 快速开始

1. **启用测试功能**：确保 `static/config/app-config.js` 中 `debugMode: true`
2. **访问视频页面**：打开带有 `batchId` 参数的视频页面
3. **使用测试功能**：点击右侧绿色悬浮按钮（👤 测试）
4. **选择测试用户**：从弹窗中选择任意测试用户进行模拟登录

## 功能特点

✅ **完全独立**：不入侵原有业务逻辑  
✅ **便于删除**：提供一键删除脚本  
✅ **环境隔离**：只在测试环境显示  
✅ **完整模拟**：支持视频观看和答题功能  
✅ **用户友好**：美观的界面设计  

## 文件清单

```
测试功能相关文件：
├── components/TestUserSelector.vue     # 测试用户选择组件
├── utils/testUserService.js           # 测试用户数据服务
├── docs/测试用户功能说明.md            # 详细说明文档
├── scripts/remove-test-features.js    # 删除脚本
└── README-测试功能.md                  # 本文件

原有文件的最小化修改：
└── pages/video/index.vue              # 只添加了几行代码
```

## 删除测试功能

当不再需要测试功能时，运行删除脚本：

```bash
cd UI
node scripts/remove-test-features.js
```

删除后：
- 所有测试相关文件被删除
- 视频页面恢复原始状态
- 原有业务逻辑完全不受影响

## 测试用户列表

系统提供10个测试用户，包括：
- 小明同学、阳光女孩、健康达人等
- 所有用户都已通过审核
- 支持完整的视频观看和答题功能

详细信息请查看 `docs/测试用户功能说明.md`

## 技术说明

- **环境检测**：自动检测测试环境
- **存储兼容**：使用相同的存储键名
- **事件通信**：组件间通过事件通信
- **样式隔离**：使用scoped样式避免冲突

---

**注意**：此功能仅用于开发测试，生产环境部署前请运行删除脚本。
