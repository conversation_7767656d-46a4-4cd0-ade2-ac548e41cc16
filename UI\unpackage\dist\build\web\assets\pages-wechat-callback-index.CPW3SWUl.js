import{_ as s,L as e,K as a,J as t,p as o,r,b as i,c as n,w as c,a4 as l,i as d,e as h,f as u,g,o as I,h as f,j as m,t as v,k as w,l as p}from"./index-BbEPSs4u.js";import k from"./utils-wechatWebAuth.BbxsB9sM.js";import{a as y}from"./wechatUserService.Dc9UmOEa.js";import"./video-user.C-TCm_6q.js";const _=s({data:()=>({isLoading:!0,hasError:!1,isSuccess:!1,errorMessage:"",loadingText:"正在处理授权信息...",callbackResult:null,retryCount:0,maxRetries:3}),onLoad(s){console.log("微信授权回调页面加载，参数:",s),this.handleWechatCallback()},methods:{async handleWechatCallback(){try{this.isLoading=!0,this.hasError=!1,this.loadingText="正在处理授权信息...";const s=k.handleCallback();if(this.callbackResult=s,console.log("微信授权回调结果:",s),!s.success)return void this.showError(s.message||"授权失败");if(!s.code)return void this.showError("未获取到授权码");await this.performLogin(s)}catch(s){console.error("处理微信授权回调失败:",s),this.showError("处理授权信息失败: "+s.message)}},async performLogin(s){var e,a;try{this.loadingText="正在登录...";const t={Code:s.code,State:s.state};console.log("InviterId获取调试:",{authSessionData:s.authSessionData,urlInviterId:s.inviterId,stateDataSharerID:null==(e=s.stateData)?void 0:e.sharerId,stateDataInviterId:null==(a=s.stateData)?void 0:a.InviterId}),s.authSessionData&&s.authSessionData.inviterId?(t.InviterId=s.authSessionData.inviterId,console.log("使用缓存会话数据中的InviterId:",s.authSessionData.inviterId)):s.inviterId?(t.InviterId=s.inviterId,console.log("使用URL参数中的InviterId:",s.inviterId)):s.stateData&&(s.stateData.sharerId?(t.InviterId=s.stateData.sharerId,console.log("使用状态数据中的sharerId:",s.stateData.sharerId)):s.stateData.InviterId&&(t.InviterId=s.stateData.InviterId,console.log("使用状态数据中的InviterId:",s.stateData.InviterId))),console.log("最终的InviterId:",t.InviterId),console.log("准备调用登录接口，数据:",t);const o=await y(t);if(console.log("登录接口响应:",o),!o.success||!o.data)throw new Error(o.message||"登录失败");await this.handleLoginSuccess(o.data)}catch(t){console.error("登录失败:",t),this.showError("登录失败: "+t.message)}},async handleLoginSuccess(s){try{this.loadingText="授权成功，正在跳转...",1===s.auditStatus?(console.log("用户已审核通过，缓存用户信息"),s.userInfo&&e("wechatUserInfo",s.userInfo),s.token&&e("wechatUserToken",s.token)):(console.log("用户未审核通过，不缓存用户信息，状态:",s.auditStatus),a("wechatUserInfo"),a("wechatUserToken")),this.isLoading=!1,this.isSuccess=!0,this.showToastMessage("授权成功！","success"),setTimeout((()=>{this.redirectAfterLogin(s)}),1500)}catch(t){console.error("处理登录成功失败:",t),this.showError("保存登录信息失败: "+t.message)}},redirectAfterLogin(s){try{const r=t("wechat_page_params")||{};console.log("缓存的页面参数:",r),e("wechat_auth_status",{completed:!0,timestamp:Date.now()});let i=r.returnUrl||"/pages/video/index";const n=[];if(r.batchId&&n.push(`batchId=${r.batchId}`),r.sharerId&&n.push(`sharerId=${r.sharerId}`),1!==s.auditStatus&&s.userInfo){const a={userInfo:s.userInfo,auditStatus:s.auditStatus};e("tempUnauditedUser",a),console.log("临时存储未审核用户信息（一次性使用）:",a)}n.length>0&&(i+="?"+n.join("&")),console.log("登录成功，跳转到纯净URL:",i),a("wechat_page_params"),o({url:i,fail:s=>{console.error("跳转失败:",s),o({url:"/pages/index/index"})}})}catch(r){console.error("跳转失败:",r),this.showError("跳转失败: "+r.message)}},showError(s){this.isLoading=!1,this.hasError=!0,this.errorMessage=s,this.showToastMessage(s,"error")},async handleRetry(){this.retryCount>=this.maxRetries?this.showToastMessage("重试次数过多，请稍后再试","warning"):(this.retryCount++,console.log(`第 ${this.retryCount} 次重试`),await this.handleWechatCallback())},goBack(){o({url:"/pages/video/index"})},showToastMessage(s,e="success"){this.$refs.uToast.show({message:s,type:e,duration:3e3})}}},[["render",function(s,e,a,t,o,k){const y=r(i("u-loading-icon"),l),_=p,D=d,S=r(i("u-icon"),h),b=r(i("u-button"),u),L=r(i("u-toast"),g);return I(),n(D,{class:"callback-container"},{default:c((()=>[f(D,{class:"callback-content"},{default:c((()=>[o.isLoading?(I(),n(D,{key:0,class:"loading-section"},{default:c((()=>[f(y,{mode:"spinner",size:"40",color:"#186BFF"}),f(_,{class:"loading-text"},{default:c((()=>[m(v(o.loadingText),1)])),_:1})])),_:1})):o.hasError?(I(),n(D,{key:1,class:"error-section"},{default:c((()=>[f(S,{name:"close-circle",size:"60",color:"#F5222D"}),f(_,{class:"error-title"},{default:c((()=>[m("授权失败")])),_:1}),f(_,{class:"error-message"},{default:c((()=>[m(v(o.errorMessage),1)])),_:1}),f(b,{type:"primary",onClick:k.handleRetry,class:"retry-btn"},{default:c((()=>[m(" 重试 ")])),_:1},8,["onClick"]),f(b,{type:"info",onClick:k.goBack,class:"back-btn"},{default:c((()=>[m(" 返回 ")])),_:1},8,["onClick"])])),_:1})):o.isSuccess?(I(),n(D,{key:2,class:"success-section"},{default:c((()=>[f(S,{name:"checkmark-circle",size:"60",color:"#52C41A"}),f(_,{class:"success-title"},{default:c((()=>[m("授权成功")])),_:1}),f(_,{class:"success-message"},{default:c((()=>[m("正在跳转...")])),_:1})])),_:1})):w("",!0)])),_:1}),f(L,{ref:"uToast"},null,512)])),_:1})}],["__scopeId","data-v-47c3d2ac"]]);export{_ as default};
