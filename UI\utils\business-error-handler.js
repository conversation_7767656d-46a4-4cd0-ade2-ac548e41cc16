/**
 * 统一业务错误处理器
 * 处理HTTP 200 + 业务code 500的情况，提供统一的错误提示
 */

/**
 * 业务错误类型定义
 */
export const BUSINESS_ERROR_TYPES = {
  VALIDATION_ERROR: 'validation_error',      // 参数验证错误
  PERMISSION_ERROR: 'permission_error',      // 权限错误
  BUSINESS_LOGIC_ERROR: 'business_logic_error', // 业务逻辑错误
  DATA_NOT_FOUND: 'data_not_found',         // 数据不存在
  OPERATION_FAILED: 'operation_failed',      // 操作失败
  UNKNOWN_ERROR: 'unknown_error'             // 未知错误
}

/**
 * 错误提示配置
 */
const ERROR_MESSAGE_CONFIG = {
  // 默认错误消息
  defaultMessage: '操作失败，请稍后重试',
  
  // 错误类型对应的默认消息
  typeMessages: {
    [BUSINESS_ERROR_TYPES.VALIDATION_ERROR]: '参数验证失败',
    [BUSINESS_ERROR_TYPES.PERMISSION_ERROR]: '权限不足',
    [BUSINESS_ERROR_TYPES.BUSINESS_LOGIC_ERROR]: '业务处理失败',
    [BUSINESS_ERROR_TYPES.DATA_NOT_FOUND]: '数据不存在',
    [BUSINESS_ERROR_TYPES.OPERATION_FAILED]: '操作失败',
    [BUSINESS_ERROR_TYPES.UNKNOWN_ERROR]: '未知错误'
  }
}

/**
 * 统一的错误提示样式配置
 */
const TOAST_CONFIG = {
  duration: 3000,
  icon: 'none',
  mask: false,
  position: 'center'
}

/**
 * 显示统一样式的错误提示
 * @param {string} message - 错误消息
 * @param {Object} options - 自定义配置
 */
export function showBusinessError(message, options = {}) {
  const config = {
    ...TOAST_CONFIG,
    ...options,
    title: message || ERROR_MESSAGE_CONFIG.defaultMessage
  }
  
  uni.showToast(config)
}

/**
 * 显示统一样式的成功提示
 * @param {string} message - 成功消息
 * @param {Object} options - 自定义配置
 */
export function showBusinessSuccess(message, options = {}) {
  const config = {
    ...TOAST_CONFIG,
    ...options,
    title: message || '操作成功',
    icon: 'success'
  }
  
  uni.showToast(config)
}

/**
 * 显示统一样式的警告提示
 * @param {string} message - 警告消息
 * @param {Object} options - 自定义配置
 */
export function showBusinessWarning(message, options = {}) {
  const config = {
    ...TOAST_CONFIG,
    ...options,
    title: message || '请注意',
    icon: 'none'
  }
  
  uni.showToast(config)
}

/**
 * 判断是否为业务错误
 * @param {Object} response - API响应对象
 * @returns {boolean} 是否为业务错误
 */
export function isBusinessError(response) {
  if (!response || typeof response !== 'object') {
    return false
  }
  
  // 新API格式: {success: boolean, code: number, msg: string, data: any}
  if (typeof response.success === 'boolean') {
    return !response.success && response.code === 500
  }
  
  // 旧API格式: {code: number, msg: string, data: any}
  return response.code === 500
}

/**
 * 判断是否为系统错误（HTTP状态码500）
 * @param {number} statusCode - HTTP状态码
 * @returns {boolean} 是否为系统错误
 */
export function isSystemError(statusCode) {
  return statusCode >= 500
}

/**
 * 获取错误消息
 * @param {Object} response - API响应对象
 * @param {string} defaultMessage - 默认错误消息
 * @returns {string} 错误消息
 */
export function getErrorMessage(response, defaultMessage = null) {
  if (!response || typeof response !== 'object') {
    return defaultMessage || ERROR_MESSAGE_CONFIG.defaultMessage
  }
  
  // 优先使用响应中的错误消息
  const message = response.msg || response.message || response.error
  if (message && typeof message === 'string') {
    return message
  }
  
  // 使用默认消息
  return defaultMessage || ERROR_MESSAGE_CONFIG.defaultMessage
}

/**
 * 处理业务错误的核心方法
 * @param {Object} response - API响应对象
 * @param {Object} options - 处理选项
 * @param {boolean} options.showToast - 是否显示错误提示，默认true
 * @param {string} options.customMessage - 自定义错误消息
 * @param {Function} options.onError - 错误回调函数
 * @returns {Object} 处理结果
 */
export function handleBusinessError(response, options = {}) {
  const {
    showToast = true,
    customMessage = null,
    onError = null
  } = options
  
  // 获取错误消息
  const errorMessage = customMessage || getErrorMessage(response)
  
  // 显示错误提示
  if (showToast) {
    showBusinessError(errorMessage)
  }
  
  // 执行错误回调
  if (typeof onError === 'function') {
    try {
      onError(response, errorMessage)
    } catch (callbackError) {
      // 静默处理回调错误
    }
  }
  
  // 返回处理结果
  return {
    handled: true,
    message: errorMessage,
    originalResponse: response
  }
}

/**
 * 业务错误处理器配置
 */
export const BusinessErrorHandler = {
  /**
   * 配置全局错误处理选项
   * @param {Object} config - 配置选项
   */
  configure(config = {}) {
    if (config.defaultMessage) {
      ERROR_MESSAGE_CONFIG.defaultMessage = config.defaultMessage
    }
    
    if (config.typeMessages) {
      Object.assign(ERROR_MESSAGE_CONFIG.typeMessages, config.typeMessages)
    }
    
    if (config.toastConfig) {
      Object.assign(TOAST_CONFIG, config.toastConfig)
    }
  },
  
  /**
   * 处理API响应，自动识别并处理业务错误
   * @param {Object} response - API响应对象
   * @param {Object} options - 处理选项
   * @returns {Object} 处理结果
   */
  handle(response, options = {}) {
    if (isBusinessError(response)) {
      return handleBusinessError(response, options)
    }
    
    return {
      handled: false,
      message: null,
      originalResponse: response
    }
  },
  
  /**
   * 检查响应是否需要业务错误处理
   * @param {Object} response - API响应对象
   * @returns {boolean} 是否需要处理
   */
  shouldHandle(response) {
    return isBusinessError(response)
  }
}

/**
 * 默认导出
 */
export default BusinessErrorHandler

/**
 * 使用示例：
 * 
 * // 基本用法
 * import { BusinessErrorHandler } from '@/utils/business-error-handler.js'
 * 
 * const response = await api.someMethod()
 * const result = BusinessErrorHandler.handle(response)
 * if (result.handled) {
 *   // 业务错误已处理
 *   return
 * }
 * 
 * // 自定义配置
 * BusinessErrorHandler.configure({
 *   defaultMessage: '自定义默认错误消息',
 *   toastConfig: {
 *     duration: 5000
 *   }
 * })
 * 
 * // 自定义处理选项
 * BusinessErrorHandler.handle(response, {
 *   customMessage: '自定义错误消息',
 *   onError: (response, message) => {
 *     console.log('业务错误:', message)
 *   }
 * })
 */
