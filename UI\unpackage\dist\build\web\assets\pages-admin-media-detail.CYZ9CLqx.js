import{_ as e,V as t,T as s,q as o,u as a,v as i,au as l,r as n,b as c,c as d,w as r,a4 as u,i as p,Y as f,f as h,av as m,aw as _,ah as v,o as w,h as x,j as g,t as y,k as I,A as z,B as C,F as q,l as A,Q as b,n as k}from"./index-BbEPSs4u.js";import{d as D,g as S}from"./video.CdWsLAeF.js";import{m as T}from"./media-common.CwXDcy84.js";import{s as P,a as V,b as j}from"./admin-helpers.CStjRvfB.js";import{p as F}from"./permission-mixin.0SnNiQ4H.js";const U=e({mixins:[T,F],data:()=>({videoId:0,videoInfo:{},quizzes:[],showDeletePopup:!1,showActionPopup:!1}),computed:{quizCount(){return this.quizzes.length},totalReward(){return this.videoInfo.rewardAmount||0},rewardText(){return"奖励: "+this.totalReward+"元"},actionList(){const e=[];return this.canUseFeature("edit_video")&&e.push({name:"编辑视频",icon:"edit-pen",color:"#3c9cff"}),this.canUseFeature("create_batch")&&e.push({name:"创建批次",icon:"plus-circle",color:"#5ac725"}),this.canUseFeature("delete_video")&&e.push({name:"删除视频",icon:"trash",color:"#f56c6c"}),e}},onLoad(e){const{id:t}=e??{};if(t){if(this.videoId=P(t),!this.videoId)return void V("视频ID无效");this.loadVideoInfo()}else V("参数错误")},onShow(){this.videoId&&this.loadVideoInfo()},methods:{async loadVideoInfo(){const e=await j((()=>S(this.videoId)),"加载视频详情...","加载视频详情失败");if(!e.success||!e.data)throw new Error(e.msg||"获取视频详情失败");{const t=e.data;this.videoInfo={id:t.id,title:t.title,cover:this.buildCompleteFileUrl(t.coverUrl)??"/assets/images/video-cover.jpg",url:this.buildCompleteFileUrl(t.videoUrl)??"https://www.runoob.com/try/demo_source/mov_bbb.mp4",duration:this.formatDuration(t.duration),uploadTime:t.createTime,uploader:t.creatorName??"未知",uploaderId:t.creatorId??"",views:t.viewCount??0,likes:t.likeCount??0,description:t.description??"",status:this.mapVideoStatus(t.status),rewardAmount:t.rewardAmount??0},this.processQuestions(t.questions??[])}},mapVideoStatus:e=>({0:"offline",1:"online",2:"failed",3:"compressing"}[e]||"offline"),processQuestions(e){try{e&&e.length>0?this.quizzes=e.map(((e,t)=>{const s=(e.options||[]).map(((e,t)=>({id:String.fromCharCode(65+t),text:e.optionText||e.text||`选项${t+1}`})));let o="A";if(e.options&&e.options.length>0){const t=e.options.find((e=>e.isCorrect));if(t){const s=e.options.indexOf(t);o=String.fromCharCode(65+s)}}return{id:e.id||t+1,question:e.questionText||e.question||`问题${t+1}`,options:s,correctAnswer:o,explanation:e.explanation||""}})):this.quizzes=[]}catch(t){console.error("处理问题数据失败:",t),this.quizzes=[]}},editVideo(){t({url:`/pages/admin/media/upload?id=${this.videoId}`})},goBack(){s()},showDeleteConfirm(){this.showDeletePopup=!0},cancelDelete(){this.showDeletePopup=!1},async handleDelete(){this.showDeletePopup=!1;try{o({title:"删除中..."});const e=await D(this.videoId);a(),e.success?(i({title:"删除成功",icon:"success"}),setTimeout((()=>{l("videoDeleted",{videoId:this.videoId}),s()}),1500)):i({title:e.msg||"删除失败",icon:"none"})}catch(e){console.error("删除视频失败:",e),a(),i({title:"删除失败，请重试",icon:"none"})}},createBatch(){t({url:`/pages/admin/media/publish?id=${this.videoId}`})},isCorrectOption:(e,t)=>"string"==typeof e.correctAnswer?e.correctAnswer===t:!!Array.isArray(e.correctAnswer)&&e.correctAnswer.includes(t),showActionSheet(){this.showActionPopup=!0},closeActionSheet(){this.showActionPopup=!1},handleActionSelect(e){switch(this.showActionPopup=!1,e.name){case"编辑视频":this.editVideo();break;case"创建批次":this.createBatch();break;case"删除视频":this.showDeleteConfirm()}}}},[["render",function(e,t,s,o,a,i){const l=n(c("u-loading-icon"),u),D=A,S=p,T=b,P=n(c("u-tag"),f),V=n(c("u-button"),h),j=n(c("u-card"),m),F=n(c("u-popup"),_),U=n(c("u-action-sheet"),v);return w(),d(S,{class:"page-container"},{default:r((()=>[x(S,{class:"video-preview-section"},{default:r((()=>["compressing"===a.videoInfo.status?(w(),d(S,{key:0,class:"video-processing-overlay"},{default:r((()=>[x(S,{class:"processing-content"},{default:r((()=>[x(l,{mode:"circle",size:"60",color:"#409eff"}),x(D,{class:"processing-text"},{default:r((()=>[g("视频压缩中，暂时无法观看")])),_:1}),x(D,{class:"processing-desc"},{default:r((()=>[g("请稍后再试")])),_:1})])),_:1})])),_:1})):"failed"===a.videoInfo.status?(w(),d(S,{key:1,class:"video-failed-overlay"},{default:r((()=>[x(S,{class:"failed-content"},{default:r((()=>[x(D,{class:"failed-icon"},{default:r((()=>[g("⚠️")])),_:1}),x(D,{class:"failed-text"},{default:r((()=>[g("视频处理失败")])),_:1}),x(D,{class:"failed-desc"},{default:r((()=>[g("请重新上传视频")])),_:1})])),_:1})])),_:1})):(w(),d(T,{key:2,src:a.videoInfo.url,poster:a.videoInfo.cover,class:"video-player",controls:""},null,8,["src","poster"])),x(P,{text:e.getStatusText(a.videoInfo),type:e.getStatusType(a.videoInfo),class:"video-status-tag"},null,8,["text","type"])])),_:1}),x(S,{class:"content-section"},{default:r((()=>[x(j,{title:a.videoInfo.title,padding:10,margin:"20rpx"},{body:r((()=>[x(S,{class:"video-info"},{default:r((()=>[x(S,{class:"info-item"},{default:r((()=>[x(D,{class:"info-label"},{default:r((()=>[g("上传时间:")])),_:1}),x(D,{class:"info-value"},{default:r((()=>[g(y(e.formatDate(a.videoInfo.uploadTime)),1)])),_:1})])),_:1}),x(S,{class:"info-item"},{default:r((()=>[x(D,{class:"info-label"},{default:r((()=>[g("上传者:")])),_:1}),x(D,{class:"info-value"},{default:r((()=>[g(y(a.videoInfo.uploader),1)])),_:1})])),_:1})])),_:1}),a.videoInfo.description?(w(),d(S,{key:0,class:"video-desc"},{default:r((()=>[x(D,null,{default:r((()=>[g(y(a.videoInfo.description),1)])),_:1})])),_:1})):I("",!0),x(S,{class:"operation-section"},{default:r((()=>[x(V,{text:"操作",type:"primary",onClick:i.showActionSheet,"custom-style":{width:"160rpx",height:"60rpx",fontSize:"28rpx"}},null,8,["onClick"])])),_:1})])),_:1},8,["title"])])),_:1}),a.quizzes.length>0?(w(),d(S,{key:0,class:"content-section"},{default:r((()=>[x(j,{padding:10,margin:"20rpx"},{head:r((()=>[x(S,{class:"quiz-card-header"},{default:r((()=>[x(D,{class:"quiz-card-title"},{default:r((()=>[g("相关问题")])),_:1}),x(P,{text:i.rewardText,type:"error",icon:"gift",size:"small"},null,8,["text"])])),_:1})])),body:r((()=>[(w(!0),z(q,null,C(a.quizzes,((e,t)=>(w(),d(S,{class:"quiz-item",key:e.id},{default:r((()=>[x(S,{class:"quiz-header"},{default:r((()=>[x(D,{class:"quiz-number"},{default:r((()=>[g("问题 "+y(t+1),1)])),_:2},1024),x(P,{text:"选择题",type:"primary",size:"mini"})])),_:2},1024),x(S,{class:"quiz-content"},{default:r((()=>[x(D,{class:"quiz-question"},{default:r((()=>[g(y(e.question),1)])),_:2},1024)])),_:2},1024),x(S,{class:"quiz-options"},{default:r((()=>[(w(!0),z(q,null,C(e.options,(t=>(w(),d(S,{class:"option-item",key:t.id},{default:r((()=>[x(D,{class:k(["option-text",{"correct-option":i.isCorrectOption(e,t.id)}])},{default:r((()=>[g(y(t.id)+". "+y(t.text)+" ",1),i.isCorrectOption(e,t.id)?(w(),d(D,{key:0,class:"correct-option-badge"},{default:r((()=>[g("✓")])),_:1})):I("",!0)])),_:2},1032,["class"])])),_:2},1024)))),128))])),_:2},1024),x(S,{class:"quiz-correct-answer"},{default:r((()=>[x(D,{class:"correct-answer-label"},{default:r((()=>[g("正确答案:")])),_:1}),x(D,{class:"correct-answer-value"},{default:r((()=>[g(y(e.correctAnswer),1)])),_:2},1024)])),_:2},1024),e.explanation?(w(),d(S,{key:0,class:"quiz-explanation"},{default:r((()=>[x(D,{class:"explanation-label"},{default:r((()=>[g("解释:")])),_:1}),x(D,{class:"explanation-content"},{default:r((()=>[g(y(e.explanation),1)])),_:2},1024)])),_:2},1024)):I("",!0)])),_:2},1024)))),128))])),_:1})])),_:1})):I("",!0),x(F,{show:a.showDeletePopup,mode:"center",closeOnClickOverlay:!0,onClose:i.cancelDelete},{default:r((()=>[x(S,{class:"delete-popup-container"},{default:r((()=>[x(S,{class:"delete-popup-header"},{default:r((()=>[x(D,{class:"delete-popup-title"},{default:r((()=>[g("确认删除")])),_:1})])),_:1}),x(S,{class:"delete-popup-body"},{default:r((()=>[x(S,{class:"delete-popup-icon"},{default:r((()=>[g("⚠️")])),_:1}),x(D,{class:"delete-popup-message"},{default:r((()=>[g("确定要删除这个视频吗？")])),_:1}),x(D,{class:"delete-popup-desc"},{default:r((()=>[g("相关的问题和奖励也会被删除，此操作不可恢复。")])),_:1})])),_:1}),x(S,{class:"delete-popup-footer"},{default:r((()=>[x(V,{text:"取消",type:"default",onClick:i.cancelDelete,"custom-style":{marginRight:"20rpx"}},null,8,["onClick"]),x(V,{text:"确认删除",type:"error",onClick:i.handleDelete},null,8,["onClick"])])),_:1})])),_:1})])),_:1},8,["show","onClose"]),x(U,{show:a.showActionPopup,actions:i.actionList,onClose:i.closeActionSheet,onSelect:i.handleActionSelect,title:"选择操作",cancelText:"取消"},null,8,["show","actions","onClose","onSelect"])])),_:1})}],["__scopeId","data-v-46c5a8e4"]]);export{U as default};
