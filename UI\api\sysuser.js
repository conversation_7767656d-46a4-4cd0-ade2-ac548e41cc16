/**
 * 系统用户管理相关API
 * 超管管理员工的系统用户接口
 * 处理系统用户的增删改查、密码管理等功能
 */

import request from '../utils/request.js'

/**
 * 系统用户创建请求参数
 * @typedef {Object} SysCreateUserDto
 * @property {string} realName - 真实姓名
 * @property {string} [avatar] - 头像URL
 * @property {string} email - 邮箱
 * @property {string} mobile - 手机号
 * @property {string} [remark] - 备注
 * @property {number} status - 状态 (1:启用 0:禁用)
 * @property {string} userName - 用户名
 * @property {string} password - 密码
 * @property {number} userType - 用户类型 (1:超管 2:管理 3:员工)
 */

/**
 * 系统用户更新请求参数
 * @typedef {Object} SysUpdateUserDto
 * @property {string} userId - 用户ID
 * @property {string} [realName] - 真实姓名
 * @property {string} [avatar] - 头像URL
 * @property {string} [email] - 邮箱
 * @property {string} [mobile] - 手机号
 * @property {string} [remark] - 备注
 * @property {number} [status] - 状态 (1:启用 0:禁用)
 */

/**
 * 系统用户详情数据
 * @typedef {Object} SysUserDto
 * @property {string} id - 用户ID
 * @property {string} userName - 用户名
 * @property {string} realName - 真实姓名
 * @property {string} [avatar] - 头像URL
 * @property {string} email - 邮箱
 * @property {string} mobile - 手机号
 * @property {string} [remark] - 备注
 * @property {number} status - 状态 (1:启用 0:禁用)
 * @property {string} roleName - 角色名称
 * @property {string} roleCode - 角色代码
 * @property {string} [lastLoginTime] - 最后登录时间
 * @property {string} [lastLoginIp] - 最后登录IP
 * @property {string} createTime - 创建时间
 * @property {string} updateTime - 更新时间
 */

/**
 * 带统计数据的系统用户详情数据
 * @typedef {Object} SysUserWithStatisticsDto
 * @property {string} id - 用户ID
 * @property {string} userName - 用户名
 * @property {string} realName - 真实姓名
 * @property {string} [avatar] - 头像URL
 * @property {string} email - 邮箱
 * @property {string} mobile - 手机号
 * @property {string} [remark] - 备注
 * @property {number} status - 状态 (1:启用 0:禁用)
 * @property {string} roleName - 角色名称
 * @property {string} roleCode - 角色代码
 * @property {string} [lastLoginTime] - 最后登录时间
 * @property {string} [lastLoginIp] - 最后登录IP
 * @property {string} createTime - 创建时间
 * @property {string} updateTime - 更新时间
 * @property {number} userType - 用户类型 (1:超管 2:管理 3:员工)
 * @property {string} [parentUserId] - 上级用户ID
 * @property {string} [parentUserName] - 上级用户名
 * @property {number} directSubordinateCount - 直接下级数量
 * @property {number} totalSubordinateUserCount - 总下级用户数量
 * @property {Object} [statistics] - 统计数据
 * @property {string} userTypeName - 用户类型名称
 */

/**
 * 用户列表查询参数
 * @typedef {Object} SysUserQueryParams
 * @property {string} [startTime] - 开始时间
 * @property {string} [endTime] - 结束时间
 * @property {number} [pageIndex] - 页码
 * @property {number} [pageSize] - 每页大小
 * @property {string} [userName] - 用户名
 * @property {string} [realName] - 真实姓名
 * @property {number} [status] - 状态
 */

/**
 * 修改密码请求参数
 * @typedef {Object} SysChangePasswordDto
 * @property {string} userId - 用户ID
 * @property {string} oldPassword - 旧密码
 * @property {string} newPassword - 新密码
 */

/**
 * 重置密码请求参数
 * @typedef {Object} SysResetPasswordDto
 * @property {string} userId - 用户ID
 * @property {string} newPassword - 新密码
 */

/**
 * API响应结果类型
 * @typedef {Object} ApiResult
 * @property {number} code - 响应代码
 * @property {string} msg - 响应消息
 * @property {boolean} success - 是否成功
 * @property {*} [data] - 响应数据
 */

/**
 * 创建新用户
 * @param {SysCreateUserDto} data - 用户创建数据
 * @returns {Promise<ApiResult<string>>} 创建结果，返回用户ID
 */
export function createSysUser (data) {
  return request.post('/SysUser/create', data)
}

/**
 * 更新用户信息
 * @param {SysUpdateUserDto} data - 用户更新数据
 * @returns {Promise<ApiResult>} 更新结果
 */
export function updateSysUser (data) {
  return request.post('/SysUser/update', data)
}

/**
 * 删除指定用户
 * @param {string} userId - 用户ID
 * @returns {Promise<ApiResult>} 删除结果
 */
export function deleteSysUser (userId) {
  return request.post(`/SysUser/delete/${userId}`)
}

/**
 * 获取用户详细信息
 * @param {string} userId - 用户ID
 * @returns {Promise<ApiResult<SysUserDto>>} 用户详情
 */
export function getSysUserDetail (userId) {
  return request.get(`/SysUser/${userId}`)
}

/**
 * 修改用户密码
 * @param {SysChangePasswordDto} data - 密码修改数据
 * @returns {Promise<ApiResult>} 修改结果
 */
export function changeSysUserPassword (data) {
  return request.post('/SysUser/change-password', data)
}

/**
 * 重置用户密码
 * @param {SysResetPasswordDto} data - 密码重置数据
 * @returns {Promise<ApiResult>} 重置结果
 */
export function resetSysUserPassword (data) {
  return request.post('/SysUser/reset-password', data)
}

/**
 * 获取管理员列表（带统计数据）
 * @param {SysUserQueryParams} params - 查询参数
 * @returns {Promise<ApiResult<Array<SysUserWithStatisticsDto>>>} 管理员列表
 */
export function getSysUserAdministrators (params = {}) {
  return request.get('/SysUser/administrators', params)
}

/**
 * 获取员工列表（带统计数据）
 * @param {SysUserQueryParams} params - 查询参数
 * @returns {Promise<ApiResult<Array<SysUserWithStatisticsDto>>>} 员工列表
 */
export function getSysUserEmployees (params = {}) {
  return request.get('/SysUser/employees', params)
}

/**
 * 获取当前用户的下级列表（带统计数据）
 * @param {SysUserQueryParams} params - 查询参数
 * @returns {Promise<ApiResult<Array<SysUserWithStatisticsDto>>>} 下级用户列表
 */
export function getSysUserSubordinates (params = {}) {
  return request.get('/SysUser/subordinates', params)
}

/**
 * 获取指定用户的下级列表（带统计数据）
 * @param {string} userId - 用户ID
 * @param {SysUserQueryParams} params - 查询参数
 * @returns {Promise<ApiResult<Array<SysUserWithStatisticsDto>>>} 下级用户列表
 */
export function getSysUserSubordinatesById (userId, params = {}) {
  return request.get(`/SysUser/subordinates/${userId}`, params)
}

/**
 * 批量删除用户
 * @param {Array<string>} userIds - 用户ID列表
 * @returns {Promise<ApiResult>} 删除结果
 */
export function batchDeleteSysUsers (userIds) {
  const promises = userIds.map(userId => deleteSysUser(userId))
  return Promise.all(promises)
}

/**
 * 切换用户状态（启用/禁用）
 * @param {string} userId - 用户ID
 * @param {number} status - 状态 (1:启用 0:禁用)
 * @returns {Promise<ApiResult>} 更新结果
 */
export function toggleSysUserStatus (userId, status) {
  return updateSysUser({
    userId,
    status
  })
}

/**
 * 获取用户类型选项
 * @returns {Array<{value: number, label: string}>} 用户类型选项
 */
export function getUserTypeOptions () {
  return [
    { value: 1, label: '超管' },
    { value: 2, label: '管理' },
    { value: 3, label: '员工' }
  ]
}

/**
 * 获取用户状态选项
 * @returns {Array<{value: number, label: string}>} 用户状态选项
 */
export function getUserStatusOptions () {
  return [
    { value: 1, label: '启用' },
    { value: 0, label: '禁用' }
  ]
}

/**
 * 验证用户数据
 * @param {SysCreateUserDto|SysUpdateUserDto} data - 用户数据
 * @param {boolean} isUpdate - 是否为更新操作
 * @returns {Object} 验证结果 {valid: boolean, errors: Array<string>}
 */
export function validateSysUserData (data, isUpdate = false) {
  const errors = []

  if (!isUpdate) {
    // 创建时必填字段验证
    if (!data.userName || data.userName.trim() === '') {
      errors.push('用户名不能为空')
    }
    if (!data.password || data.password.trim() === '') {
      errors.push('密码不能为空')
    }
    if (!data.realName || data.realName.trim() === '') {
      errors.push('真实姓名不能为空')
    }
    if (!data.email || data.email.trim() === '') {
      errors.push('邮箱不能为空')
    }
    if (!data.mobile || data.mobile.trim() === '') {
      errors.push('手机号不能为空')
    }
    if (data.userType === undefined || data.userType === null) {
      errors.push('用户类型不能为空')
    }
  } else {
    // 更新时必填字段验证
    if (!data.userId || data.userId.trim() === '') {
      errors.push('用户ID不能为空')
    }
  }

  // 邮箱格式验证
  if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errors.push('邮箱格式不正确')
  }

  // 手机号格式验证
  if (data.mobile && !/^1[3-9]\d{9}$/.test(data.mobile)) {
    errors.push('手机号格式不正确')
  }

  // 密码强度验证（仅创建时）
  if (!isUpdate && data.password) {
    if (data.password.length < 6) {
      errors.push('密码长度不能少于6位')
    }
    if (!/(?=.*[a-zA-Z])(?=.*\d)/.test(data.password)) {
      errors.push('密码必须包含字母和数字')
    }
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 获取当前管理员的用户审核权限配置
 * @returns {Promise<ApiResult<boolean>>} 审核权限配置
 */
export function getManagerAuditConfig () {
  return request.get('/SysUser/audit-config')
}

/**
 * 设置当前管理员的用户审核权限配置
 * @param {boolean} enableAudit - 是否开启审核
 * @returns {Promise<ApiResult<boolean>>} 设置结果
 */
export function setManagerAuditConfig (enableAudit) {
  // 确保布尔值被正确序列化为JSON字符串
  // 这样false不会被认为是空请求体
  const jsonData = JSON.stringify(enableAudit);

  return request.post('/SysUser/audit-config', jsonData, {
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 默认导出所有系统用户相关API
export default {
  createSysUser,
  updateSysUser,
  deleteSysUser,
  getSysUserDetail,
  changeSysUserPassword,
  resetSysUserPassword,
  getSysUserAdministrators,
  getSysUserEmployees,
  getSysUserSubordinates,
  getSysUserSubordinatesById,
  batchDeleteSysUsers,
  toggleSysUserStatus,
  getUserTypeOptions,
  getUserStatusOptions,
  validateSysUserData,
  getManagerAuditConfig,
  setManagerAuditConfig
}
