import{_ as e,v as t,o as s,c as a,w as o,h as i,j as r,n,t as c,k as d,A as l,B as u,F as h,l as m,i as w,C as f,D as p,E as g,G as A,H as y,I as v,J as _,K as I,L as C,M as b,u as T,q as x,N as R,p as U,O as q,P as k,x as S,Q as P,R as D}from"./index-BbEPSs4u.js";import{w as O}from"./wechatUserService.Dc9UmOEa.js";import{g as z}from"./batch.DlOJUAFN.js";import{m as V}from"./media-common.CwXDcy84.js";import"./video-user.C-TCm_6q.js";const E={name:"VideoQuiz",props:{questions:{type:Array,default:()=>[]},rewardAmount:{type:Number,default:0},videoCompleted:{type:Boolean,default:!1},hasAnswered:{type:Boolean,default:!1},answerResult:{type:Object,default:()=>null}},data:()=>({selectedAnswers:[],showResults:!1,correctCount:0,showResult:!1,earnedAmount:0}),computed:{canSubmit(){return!(!this.questions||0===this.questions.length)&&(this.selectedAnswers.length===this.questions.length&&!this.selectedAnswers.includes(void 0))}},watch:{questions:{immediate:!0,handler(e){e&&e.length>0&&(this.selectedAnswers=new Array(e.length))}},answerResult:{immediate:!0,handler(e){e&&e.answerDetails&&this.hasAnswered&&(this.restoreUserAnswers(),this.showResults=!0)}}},methods:{selectAnswer(e,s){this.hasAnswered?t({title:"您已完成答题，无法重复作答",icon:"none"}):this.videoCompleted?this.showResults||(this.$set(this.selectedAnswers,e,s),this.canSubmit&&t({title:"已回答所有问题，可以提交",icon:"none",duration:1500})):t({title:"请先观看完视频",icon:"none"})},submitAnswers(){if(this.hasAnswered)return void t({title:"您已完成答题，无法重复作答",icon:"none"});if(!this.videoCompleted)return void t({title:"请先观看完视频",icon:"none"});if(!this.canSubmit){const e=this.questions.length-this.selectedAnswers.filter(Boolean).length;return void t({title:`还有${e}个问题未回答`,icon:"none"})}this.correctCount=0;const e=[];this.questions.forEach(((t,s)=>{const a=this.selectedAnswers[s]===t.correctAnswer;a&&this.correctCount++;const o=t.options.find((e=>e.id===this.selectedAnswers[s])),i=t.options.find((e=>e.id===t.correctAnswer));e.push({questionOrderNum:s+1,questionText:t.question,selectedOptionOrderNum:o?t.options.indexOf(o)+1:0,selectedOptionText:o?o.text||o.optionText:"",correctOptionText:i?i.text||i.optionText:"",isCorrect:a})}));const s=this.correctCount/this.questions.length;this.earnedAmount=(this.rewardAmount*s).toFixed(2),this.showResults=!0,this.$emit("submit",{answers:this.selectedAnswers,correctCount:this.correctCount,earnedAmount:this.earnedAmount,totalQuestions:this.questions.length,answerDetails:e}),setTimeout((()=>{this.showResult=!0}),1e3)},closeResult(){this.showResult=!1,this.$emit("complete")},reset(){this.selectedAnswers=new Array(this.questions.length),this.showResults=!1,this.correctCount=0,this.showResult=!1,this.earnedAmount=0},restoreUserAnswers(){if(this.answerResult&&this.answerResult.answerDetails&&this.questions)try{let e=this.answerResult.answerDetails;"string"==typeof e&&(e=JSON.parse(e)),e.forEach(((e,t)=>{const s=e.questionOrderNum?e.questionOrderNum-1:t;if(s>=0&&s<this.questions.length&&this.questions[s]){const t=this.questions[s];let a=null;if(e.selectedOption&&(a=t.options.find((t=>t.id===e.selectedOption))),!a&&e.selectedOptionText&&(a=t.options.find((t=>t.text&&t.text.trim()===e.selectedOptionText.trim()||t.optionText&&t.optionText.trim()===e.selectedOptionText.trim()))),!a&&e.selectedOptionOrderNum){const s=e.selectedOptionOrderNum-1;s>=0&&s<t.options.length&&(a=t.options[s])}a&&this.$set(this.selectedAnswers,s,a.id)}})),this.correctCount=this.answerResult.correctAnswers||0,this.earnedAmount=this.answerResult.rewardAmount||0}catch(e){console.error("恢复用户答案失败:",e)}},getQuizStatusText(){return this.hasAnswered?"(已完成答题)":this.videoCompleted?"(可以答题了)":"(请先观看完视频)"},formatAnswerTime(e){if(!e)return"";try{return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(t){return e}}}};async function W(e,t={}){O.logout();try{const e=await O.tryWechatAutoLogin(t);if(e.success)return!0;throw new Error(e.message||"重新授权失败")}catch(s){return g("登录已过期，请刷新页面重新登录"),!1}}async function L(e){const t=await function(e,t="提示"){return new Promise((s=>{v({title:t,content:e,confirmText:"确认",cancelText:"取消",success:e=>{s(e.confirm)},fail:()=>{s(!1)}})}))}("无法连接到服务器，是否重试？","连接错误");return t||g("已取消重试"),t}function $(e){return new Promise(((t,s)=>{const a=function(e){e.url.startsWith("http")||(e.url=A()+e.url),e.header={...y.DEFAULT_HEADERS,...e.header};const t=O.getUserToken();return t&&(e.header.Authorization=`Bearer ${t}`),e.timeout=e.timeout||y.TIMEOUT,e}(e);p({...a,success:a=>{(async function(e,t){const{data:s,statusCode:a}=e;if(200!==a)return 401===a?await W(0,M.getPageOptions())?$(t):Promise.reject(new Error("认证失败")):a>=500?(g("服务器错误"),Promise.reject(new Error("服务器错误"))):(g(`请求失败 (${a})`),Promise.reject(new Error(`请求失败 (${a})`)));if(s&&"object"==typeof s){if("boolean"==typeof s.success){if(s.success)return Promise.resolve(s);{const e=s.msg||"请求失败";return 401===s.code?await W(0,M.getPageOptions())?$(t):Promise.reject(new Error(e)):Promise.resolve(s)}}const{code:e,msg:a,message:o}=s;return 200===e||0===e||500===e?Promise.resolve(s):401===e?await W(0,M.getPageOptions())?$(t):Promise.reject(new Error(a||o||"认证失败")):Promise.resolve(s)}return Promise.resolve(s)})(a,e).then(t).catch(s)},fail:async a=>{if(a.errMsg&&a.errMsg.includes("timeout"))g("请求超时，请检查网络连接"),s(a);else if(a.errMsg&&a.errMsg.includes("fail")){await L()?$(e).then(t).catch(s):s(a)}else g("请求配置错误: "+(a.errMsg||a.message||"未知错误")),s(a)}})}))}const M={get:(e,t={},s={})=>$({url:e,method:"GET",data:t,...s}),post:(e,t={},s={})=>$({url:e,method:"POST",data:t,...s}),put:(e,t={},s={})=>$({url:e,method:"PUT",data:t,...s}),delete:(e,t={},s={})=>$({url:e,method:"DELETE",data:t,...s}),setPageOptions(e){this._pageOptions=e},getPageOptions(){return this._pageOptions||{}}};const Q=e({name:"VideoIndex",mixins:[V],components:{VideoQuiz:e(E,[["render",function(e,t,p,g,A,y){const v=m,_=w,I=f;return s(),a(_,{class:"quiz-box"},{default:o((()=>[i(_,{class:"quiz-header"},{default:o((()=>[i(v,{class:"quiz-title"},{default:o((()=>[r("视频问答")])),_:1}),i(v,{class:n(["quiz-note",{ready:p.videoCompleted,answered:p.hasAnswered}])},{default:o((()=>[r(c(y.getQuizStatusText()),1)])),_:1},8,["class"])])),_:1}),p.hasAnswered&&p.answerResult?(s(),a(_,{key:0,class:"answer-summary"},{default:o((()=>[i(_,{class:"summary-item"},{default:o((()=>[i(v,{class:"summary-label"},{default:o((()=>[r("答题得分：")])),_:1}),i(v,{class:n(["summary-value",{"high-score":p.answerResult.correctRate>=80}])},{default:o((()=>[r(c(p.answerResult.correctAnswers)+"/"+c(p.answerResult.totalQuestions)+" ("+c(p.answerResult.correctRate)+"%) ",1)])),_:1},8,["class"])])),_:1}),p.answerResult.rewardAmount>0?(s(),a(_,{key:0,class:"summary-item"},{default:o((()=>[i(v,{class:"summary-label"},{default:o((()=>[r("获得奖励：")])),_:1}),i(v,{class:"summary-value reward"},{default:o((()=>[r(c(p.answerResult.rewardAmount)+"元",1)])),_:1})])),_:1})):d("",!0),i(_,{class:"summary-item"},{default:o((()=>[i(v,{class:"summary-label"},{default:o((()=>[r("答题时间：")])),_:1}),i(v,{class:"summary-value"},{default:o((()=>[r(c(y.formatAnswerTime(p.answerResult.answerTime)),1)])),_:1})])),_:1})])),_:1})):d("",!0),i(_,{class:"question-list"},{default:o((()=>[(s(!0),l(h,null,u(p.questions,((e,t)=>(s(),a(_,{class:"question-item",key:t},{default:o((()=>[i(_,{class:"question-text"},{default:o((()=>[i(v,{class:"q-number"},{default:o((()=>[r(c(t+1)+". ",1)])),_:2},1024),i(v,{class:"q-content"},{default:o((()=>[r(c(e.question),1)])),_:2},1024)])),_:2},1024),i(_,{class:"options"},{default:o((()=>[(s(!0),l(h,null,u(e.options,(d=>(s(),a(_,{class:n(["option",{selected:A.selectedAnswers[t]===d.id,correct:A.showResults&&d.id===e.correctAnswer,wrong:A.showResults&&A.selectedAnswers[t]===d.id&&d.id!==e.correctAnswer,disabled:!p.videoCompleted||p.hasAnswered}]),key:d.id,onClick:e=>y.selectAnswer(t,d.id)},{default:o((()=>[i(v,{class:"option-label"},{default:o((()=>[r(c(d.id),1)])),_:2},1024),i(v,{class:"option-text"},{default:o((()=>[r(c(d.text||d.optionText),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:2},1024)])),_:2},1024)))),128))])),_:1}),i(I,{class:n(["submit-btn",{disabled:!y.canSubmit||!p.videoCompleted||p.hasAnswered}]),disabled:!y.canSubmit||!p.videoCompleted||p.hasAnswered,onClick:y.submitAnswers},{default:o((()=>[r(c(p.hasAnswered?"已完成答题":"提交答案"),1)])),_:1},8,["disabled","class","onClick"]),A.showResult?(s(),a(_,{key:1,class:"result-modal"},{default:o((()=>[i(_,{class:"result-content"},{default:o((()=>[i(_,{class:"result-header"},{default:o((()=>[i(v,{class:"result-title"},{default:o((()=>[r("答题结果")])),_:1})])),_:1}),i(v,{class:"result-score"},{default:o((()=>[r("得分: "+c(A.correctCount)+"/"+c(p.questions.length),1)])),_:1}),p.rewardAmount>0?(s(),a(v,{key:0,class:"result-reward"},{default:o((()=>[r("获得红包: "+c(A.earnedAmount)+"元",1)])),_:1})):d("",!0),i(I,{class:"close-btn",onClick:y.closeResult},{default:o((()=>[r("关闭")])),_:1},8,["onClick"])])),_:1})])),_:1})):d("",!0)])),_:1})}],["__scopeId","data-v-823f4864"]])},data:()=>({currentVideo:{url:"",cover:"",title:"加载中...",description:"",rewardAmount:0,duration:0},videoCompleted:!1,isFullscreen:!1,isPlaying:!1,maxWatchTime:0,currentPlayTime:0,quizData:{questions:[]},hasAnswered:!1,answerResult:null,showAuditModal:!1,auditUserInfo:null,batchId:null,sharerId:null,authCompleted:!1,viewRecordId:null,lastProgressUpdate:0,isAuthenticating:!1,wechatLoginCancelled:!1,showDevTools:!1}),async onLoad(e){try{this.batchId=e.batchId?parseInt(e.batchId):null,this.sharerId=e.sharerId;const t=_("wechat_auth_status");if(this.authCompleted=t&&!0===t.completed,this.authCompleted){I("wechat_auth_status");const e=_("tempUnauditedUser");e&&(this.auditUserInfo={nickname:e.userInfo.nickname||"",avatar:e.userInfo.avatar||"",auditStatus:e.auditStatus||0},I("tempUnauditedUser"))}if(this.cleanExpiredAuthSessions(),e.code&&e.state){console.log("检测到微信回调参数，重定向到回调页面处理");const t={batchId:this.batchId,sharerId:this.sharerId,returnUrl:"/pages/video/index"};return C("wechat_page_params",t),void b({url:`/pages/wechat-callback/index?code=${e.code}&state=${e.state}`})}await this.authenticateUser()}catch(s){console.error("页面初始化失败:",s.message),t({title:"页面加载失败",icon:"error"})}},methods:{handlePageClick(){this.wechatLoginCancelled&&!O.isLoggedIn()&&this.showWechatLoginModal()},showWechatLoginModal(){v({title:"需要微信授权",content:"请点击确定进行微信授权登录",success:async e=>{e.confirm?(this.wechatLoginCancelled=!1,await this.performWechatLogin()):(this.wechatLoginCancelled=!0,t({title:"点击页面任何地方可重新登录",icon:"none",duration:3e3}))}})},async performWechatLogin(){try{const e={batchId:this.batchId,sharerId:this.sharerId,returnUrl:"/pages/video/index"};C("wechat_page_params",e);const t={employeeId:null,batchId:this.batchId,sharerId:this.sharerId,returnUrl:"/pages/video/index"};await O.tryWechatAutoLogin(t)}catch(e){console.error("微信授权失败:",e),v({title:"授权失败",content:`微信授权失败：${e.message||"未知错误"}`,showCancel:!1,confirmText:"确定"})}},async authenticateUser(){if(!this.isAuthenticating)try{if(this.isAuthenticating=!0,O.isLoggedIn())return void(await this.loadVideoData());if(this.authCompleted)return void(this.showAuditModal=!0);this.showWechatLoginModal()}catch(e){if(T(),console.error("用户认证失败:",e),e.message&&e.message.includes("审核状态"))return void(this.showAuditModal=!0);t({title:"登录失败，请重试",icon:"error"})}finally{this.isAuthenticating=!1}},showAuditStatusModal(){const e=O.getUserInfo();this.auditUserInfo=e||{id:"unknown",nickname:"微信用户",avatar:"/static/images/default-avatar.png"},this.showAuditModal=!0},contactAdmin(){v({title:"联系管理员",content:"请通过微信群或电话联系管理员，管理员会尽快为您审核账号。",showCancel:!1,confirmText:"知道了"})},async loadVideoData(){try{if(x({title:"加载视频中...",mask:!0}),!this.batchId)throw new Error("缺少批次ID");await this.loadDataFromBatch(),await this.startWatchRecord(),await this.loadUserWatchStatus(),await this.loadUserAnswerStatus(),T()}catch(e){T(),console.error("加载视频数据失败:",e),t({title:e.message||"加载失败",icon:"error"})}},async loadDataFromBatch(){const e=await z(this.batchId);if(!e.success||!e.data)throw new Error(e.msg||"获取批次详情失败");const t=e.data,s=this.buildCompleteFileUrl(t.videoUrl),a=this.buildCompleteFileUrl(t.videoCoverUrl);this.currentVideo={id:t.videoId,title:t.videoTitle||"视频标题",cover:a||"/static/images/default-cover.jpg",url:s||"",duration:t.videoDuration||0,description:t.videoDescription||"",rewardAmount:t.rewardAmount||0},this.processQuizData(t.questions||[])},async startWatchRecord(){try{const e=O.getUserInfo();if(!e||!e.id)return;const t=await M.post("/UserBatchRecord/create-or-get",{batchId:this.batchId,userId:e.id,promotionLink:this.sharerId?`shared_by_${this.sharerId}`:null});t.success&&t.data?this.viewRecordId=t.data.id:console.error("创建观看记录失败:",t.message)}catch(e){console.error("开始记录观看失败:",e)}},processQuizData(e){e&&0!==e.length?this.quizData={questions:e.map((e=>{let t=[];if(e.options)if("string"==typeof e.options)try{t=JSON.parse(e.options)}catch(o){console.error("解析选项JSON失败:",o,e.options),t=[]}else Array.isArray(e.options)&&(t=e.options);const s=t.map(((e,t)=>({...e,id:String.fromCharCode(65+t),text:e.optionText||e.text||"",optionText:e.optionText||e.text||""})));let a=e.correctAnswer;if(!a){const e=t.findIndex((e=>e.isCorrect));e>=0&&(a=String.fromCharCode(65+e))}return{id:e.id,question:e.questionText||e.question||e.title,options:s,correctAnswer:a,type:e.type||"single"}}))}:this.quizData={questions:[]}},async loadUserWatchStatus(){try{const e=O.getUserInfo();if(!e||!e.id)return;const t=await M.get(`/UserBatchRecord/${e.id}/${this.batchId}/watch-status`);t.success&&t.data&&(t.data.id&&(this.viewRecordId=t.data.id),t.data.isCompleted&&(this.videoCompleted=!0))}catch(e){console.error("加载用户观看状态失败:",e)}},async loadUserAnswerStatus(){try{const t=O.getUserInfo();if(!t||!t.id)return;const s=await M.get(`/UserBatchRecord/${t.id}/${this.batchId}/answer-status`);if(s.success&&s.data){if(this.hasAnswered=s.data.hasAnswered||!1,s.data.hasAnswered&&(this.answerResult={totalQuestions:s.data.totalQuestions,correctAnswers:s.data.correctAnswers,correctRate:s.data.correctRate,rewardAmount:s.data.rewardAmount,rewardStatus:s.data.rewardStatus,rewardStatusText:s.data.rewardStatusText,answerTime:s.data.answerTime},s.data.answerDetails))try{const e=JSON.parse(s.data.answerDetails);this.answerResult.answerDetails=e}catch(e){console.error("解析答题详情失败:",e)}}else this.hasAnswered=!1}catch(t){console.error("加载用户答题状态失败:",t),this.hasAnswered=!1}},async onVideoEnded(){this.videoCompleted=!0,this.isPlaying=!1,this.maxWatchTime=this.currentVideo.duration||Number.MAX_VALUE,await this.updateWatchProgress(this.currentVideo.duration,!0),this.showQuizAfterVideo(),t({title:"视频播放完成，可以答题了",icon:"success",duration:2e3})},showQuizAfterVideo(){this.quizData.questions&&this.quizData.questions.length>0||this.onQuizComplete({success:!0,answers:[]})},onTimeUpdate(e){const s=e.detail.currentTime,a=e.detail.duration||this.currentVideo.duration||0;if(s>this.maxWatchTime+2){const e=R("mainVideo",this);return e&&e.seek(this.maxWatchTime),void t({title:"请完整观看视频，不允许快进",icon:"none",duration:2e3})}s>this.maxWatchTime&&(this.maxWatchTime=s),this.currentPlayTime=s,a>0&&(this.currentVideo.duration=a),s-this.lastProgressUpdate>=10&&(this.updateWatchProgress(s),this.lastProgressUpdate=s)},onSeeking(e){(e.detail.currentTime||0)>this.maxWatchTime+2&&(setTimeout((()=>{const e=R("mainVideo",this);e&&e.seek(this.maxWatchTime)}),100),t({title:"不允许快进，请完整观看",icon:"none",duration:2e3}))},onSeeked(e){if((e.detail.currentTime||0)>this.maxWatchTime+2){const e=R("mainVideo",this);e&&e.seek(this.maxWatchTime),t({title:"已回退到正确位置",icon:"none",duration:1500})}},onPlay(){this.isPlaying=!0},onPause(){this.isPlaying=!1},onLoadedMetadata(e){e.detail.duration&&(this.currentVideo.duration=e.detail.duration)},async updateWatchProgress(e,t=!1){try{if(!this.viewRecordId&&(await this.startWatchRecord(),!this.viewRecordId))return;const s=O.getUserInfo();if(!s||!s.id)return;const a=this.currentVideo.duration>0?e/this.currentVideo.duration:0,o=t||a>=.95,i=await M.post(`/UserBatchRecord/${s.id}/watch-progress`,{batchId:this.batchId,viewDuration:Math.floor(e),watchProgress:t?1:Math.min(a,1),isCompleted:o});i.success?console.log(`观看进度更新成功: ${Math.floor(100*a)}%${o?" (已完成)":""}`):console.error("更新观看进度失败:",i.message)}catch(s){console.error("更新观看进度失败:",s)}},async onQuizSubmit(e){try{const s=O.getUserInfo();if(!s||!s.id)return void console.error("用户信息不完整，无法提交答题");const a={batchId:this.batchId,totalQuestions:e.totalQuestions,correctAnswers:e.correctCount,answerDetails:JSON.stringify(e.answerDetails)},o=await M.post(`/UserBatchRecord/${s.id}/submit-answer`,a);o.success||(console.error("答题提交失败:",o.message),t({title:"答题提交失败",icon:"error"}))}catch(s){console.error("答题提交异常:",s),t({title:"答题提交失败",icon:"error"})}},onQuizComplete(){this.claimReward()},async claimReward(){try{this.sharerId&&this.recordSharerReward()}catch(e){console.error("领取红包失败:",e)}},async recordSharerReward(){},cleanUrlAndReload(){try{C("wechat_auth_status",{completed:!0,timestamp:Date.now()});const e=[];this.batchId&&e.push(`batchId=${this.batchId}`),this.sharerId&&e.push(`sharerId=${this.sharerId}`);const t="/pages/video/index"+(e.length>0?"?"+e.join("&"):"");console.log("清理授权参数，跳转到纯净URL:",t),U({url:t})}catch(e){console.error("清理URL失败:",e)}},cleanExpiredAuthSessions(){try{const e=q(),t=Date.now(),s=18e5;e.keys.forEach((e=>{if(e.startsWith("wechat_auth_session_"))try{const a=_(e);a&&a.timestamp&&t-a.timestamp>s&&(I(e),console.log("清理过期授权会话:",e))}catch(a){I(e)}}))}catch(e){console.error("清理过期授权会话失败:",e)}},clearCache(){v({title:"清除缓存",content:"确定要清除所有用户缓存数据吗？清除后需要重新登录。",success:e=>{if(e.confirm)try{O.clearUserInfo(),k(),t({title:"缓存已清除",icon:"success"}),setTimeout((()=>{U({url:`/pages/video/index?batchId=${this.batchId}${this.sharerId?`&sharerId=${this.sharerId}`:""}`})}),1500)}catch(s){console.error("清除缓存失败:",s),t({title:"清除失败",icon:"error"})}}})}},onUnload(){this.progressUpdateTimer&&(clearInterval(this.progressUpdateTimer),this.progressUpdateTimer=null)}},[["render",function(e,t,n,l,u,h){const p=P,g=m,A=w,y=S("VideoQuiz"),v=D,_=f;return s(),a(A,{class:"container",onClick:h.handlePageClick},{default:o((()=>[u.isFullscreen||u.showAuditModal?d("",!0):(s(),a(A,{key:0,class:"video-content"},{default:o((()=>[i(p,{id:"mainVideo",src:u.currentVideo.url,poster:u.currentVideo.cover,class:"video-player",controls:"","enable-progress-gesture":!1,"show-progress":!0,"page-gesture":!1,onEnded:h.onVideoEnded,onTimeupdate:h.onTimeUpdate,onSeeking:h.onSeeking,onSeeked:h.onSeeked,onPlay:h.onPlay,onPause:h.onPause,onLoadedmetadata:h.onLoadedMetadata},null,8,["src","poster","onEnded","onTimeupdate","onSeeking","onSeeked","onPlay","onPause","onLoadedmetadata"]),i(A,{class:"video-info"},{default:o((()=>[i(g,{class:"video-title"},{default:o((()=>[r(c(u.currentVideo.title),1)])),_:1}),i(g,{class:"video-desc"},{default:o((()=>[r(c(u.currentVideo.description),1)])),_:1})])),_:1})])),_:1})),!u.isFullscreen&&!u.showAuditModal&&u.quizData.questions&&u.quizData.questions.length>0?(s(),a(A,{key:1,class:"quiz-section"},{default:o((()=>[i(y,{questions:u.quizData.questions,rewardAmount:u.currentVideo.rewardAmount||0,videoCompleted:u.videoCompleted,hasAnswered:u.hasAnswered,answerResult:u.answerResult,onSubmit:h.onQuizSubmit,onComplete:h.onQuizComplete},null,8,["questions","rewardAmount","videoCompleted","hasAnswered","answerResult","onSubmit","onComplete"])])),_:1})):d("",!0),u.showAuditModal?(s(),a(A,{key:2,class:"audit-modal"},{default:o((()=>[i(A,{class:"audit-modal-content"},{default:o((()=>[i(A,{class:"audit-header"},{default:o((()=>[i(g,{class:"audit-icon"},{default:o((()=>[r("⚠️")])),_:1}),i(g,{class:"audit-title"},{default:o((()=>[r("账号待审核")])),_:1})])),_:1}),u.auditUserInfo?(s(),a(A,{key:0,class:"audit-user-info"},{default:o((()=>[i(A,{class:"user-avatar"},{default:o((()=>[i(v,{src:u.auditUserInfo.avatar,class:"avatar-img",mode:"aspectFill"},null,8,["src"])])),_:1}),i(A,{class:"user-details"},{default:o((()=>[i(g,{class:"user-name"},{default:o((()=>[r(c(u.auditUserInfo.nickname),1)])),_:1}),i(g,{class:"user-id"},{default:o((()=>[r("用户ID: "+c(u.auditUserInfo.id),1)])),_:1})])),_:1})])),_:1})):d("",!0),i(A,{class:"audit-message"},{default:o((()=>[i(g,{class:"message-text"},{default:o((()=>[r("您的账号正在审核中，暂时无法使用视频功能。")])),_:1}),i(g,{class:"contact-text"},{default:o((()=>[r("请联系管理员进行账号审核。")])),_:1})])),_:1}),i(A,{class:"audit-actions"},{default:o((()=>[i(_,{class:"contact-btn",onClick:h.contactAdmin},{default:o((()=>[r("联系管理员")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})):d("",!0),u.showDevTools?(s(),a(A,{key:3,class:"dev-tools"},{default:o((()=>[i(_,{class:"dev-btn clear-btn",onClick:h.clearCache},{default:o((()=>[r("清除缓存")])),_:1},8,["onClick"]),i(_,{class:"dev-btn hide-btn",onClick:t[0]||(t[0]=e=>u.showDevTools=!1)},{default:o((()=>[r("隐藏")])),_:1})])),_:1})):d("",!0),u.showDevTools?d("",!0):(s(),a(A,{key:4,class:"dev-toggle",onClick:t[1]||(t[1]=e=>u.showDevTools=!u.showDevTools)},{default:o((()=>[i(g,{class:"dev-icon"},{default:o((()=>[r("🔧")])),_:1})])),_:1}))])),_:1},8,["onClick"])}],["__scopeId","data-v-eba4b88b"]]);export{Q as default};
