import{_ as e,ak as t,v as a,V as s,T as r,U as o,r as i,b as l,x as n,c as d,w as c,i as u,Y as m,e as f,f as h,aa as p,o as g,h as I,A as v,j as _,t as y,ai as w,k as D,B as T,F,l as x,aj as S,al as k}from"./index-BbEPSs4u.js";import{U}from"./UserInfoCard.iqQmkj32.js";import{T as R}from"./TimeFilter.ZkD3iJM5.js";import{g as b}from"./video-user.C-TCm_6q.js";import{g as C}from"./user-batch-record.k6a6hk2v.js";import{b as z}from"./employee-data-mapper.B3YJtEOo.js";import{a as $,E as j}from"./api-error-handler.ObSHiZ2e.js";import{m as B}from"./media-common.CwXDcy84.js";import{s as M,a as P}from"./admin-helpers.CStjRvfB.js";import"./permission-mixin.0SnNiQ4H.js";const V=e({mixins:[B],components:{UserInfoCard:U,TimeFilter:R},data:()=>({userId:null,promoterId:null,userInfo:null,relatedUsers:[],activeTimeFilter:"today",customDateRange:{startDate:"",endDate:""},learningRecords:[],loaded:!1,scrollViewHeight:400}),computed:{pageTitle(){return this.userInfo&&"promoter"===this.userInfo.type?"推广员信息":"用户信息"}},onLoad(e){const{userId:t,promoterId:a}=e??{};if(t){if(this.userId=M(t),!this.userId)return void P("用户ID无效");this.loadUserData()}else{if(!a)return void P("参数错误");if(this.promoterId=M(a),!this.promoterId)return void P("推广员ID无效");this.loadPromoterData()}this.calculateScrollViewHeight()},onReady(){this.calculateScrollViewHeight()},methods:{calculateScrollViewHeight(){t({success:e=>{const t=e.windowHeight;this.scrollViewHeight=t-300}})},handleTimeFilterChange(e){this.activeTimeFilter=e,this.customDateRange={startDate:e.startDate,endDate:e.endDate},console.log("时间筛选变化:",e)},async loadUserData(){if(!this.userId)return a({title:"用户ID不能为空",icon:"none"}),void setTimeout((()=>{this.goBack()}),1500);try{const e=await $((()=>b(this.userId)),{...j.important,loadingTitle:"加载用户信息...",errorTitle:"加载失败"});e.success&&e.data?(this.userInfo=z(e.data),this.userInfo.type="user",await this.loadUserWatchRecords()):(a({title:e.msg||"获取用户信息失败",icon:"none"}),setTimeout((()=>{this.goBack()}),1500))}catch(e){console.error("加载用户数据失败:",e),a({title:"加载用户信息失败",icon:"none"}),setTimeout((()=>{this.goBack()}),1500)}},async loadPromoterData(){if(!this.promoterId)return a({title:"推广员ID不能为空",icon:"none"}),void setTimeout((()=>{this.goBack()}),1500);try{const e=await $((()=>b(this.promoterId)),{...j.important,loadingTitle:"加载推广员信息...",errorTitle:"加载失败"});e.success&&e.data?(this.userInfo=z(e.data),this.userInfo.type="promoter",console.log("推广员详情数据:",this.userInfo),await this.loadRelatedUsers()):(a({title:e.msg||"获取推广员信息失败",icon:"none"}),setTimeout((()=>{this.goBack()}),1500))}catch(e){console.error("加载推广员数据失败:",e),a({title:"加载推广员信息失败",icon:"none"}),setTimeout((()=>{this.goBack()}),1500)}},async loadRelatedUsers(){if(this.userInfo&&"promoter"===this.userInfo.type)try{this.relatedUsers=[]}catch(e){console.error("加载相关用户失败:",e),this.relatedUsers=[]}},async loadUserWatchRecords(){if(this.userId)try{const e=await $((()=>C(this.userId)),{...j.silent,loadingTitle:"加载观看记录..."});e.success&&e.data?this.learningRecords=this.formatWatchRecords(e.data):(console.warn("获取观看记录失败:",e.msg),this.learningRecords=[])}catch(e){console.error("加载观看记录失败:",e),this.learningRecords=[]}},formatWatchRecords(e){return Array.isArray(e)?e.map((e=>({title:e.batchName||"未知视频",cover:e.videoCoverUrl?this.buildCompleteFileUrl(e.videoCoverUrl):"/static/images/video-cover.jpg",watchTime:this.formatDateTime(e.createTime),duration:this.formatProgressText(e.watchProgressPercent),watched:e.watchProgressPercent>0,quizCompleted:e.hasAnswered||!1,rewardReceived:1===e.rewardStatus,quizResult:e.correctRate>.5,rewardAmount:e.rewardAmount||0,watchProgress:e.watchProgressPercent/100,isCompleted:e.isCompleted}))).sort(((e,t)=>new Date(t.watchTime)-new Date(e.watchTime))):[]},formatProgressText:e=>!e||e<=0?"未观看":e>=100?"已完播":`观看进度 ${e.toFixed(1)}%`,generateMockRecords(){const e=[],t=Math.min(this.userInfo.watchedVideos||0,5);for(let s=0;s<t;s++){const t=e[Math.floor(Math.random()*e.length)],s=new Date,r=new Date(s);r.setDate(s.getDate()-Math.floor(7*Math.random()));const o=!0,i=Math.random()>.3,l=i&&Math.random()>.2,n=!!i&&Math.random()>.2;this.learningRecords.push({title:t.title,cover:t.cover||"/static/images/video-cover.jpg",watchTime:(a=r,`${a.getFullYear()}-${String(a.getMonth()+1).padStart(2,"0")}-${String(a.getDate()).padStart(2,"0")} ${String(a.getHours()).padStart(2,"0")}:${String(a.getMinutes()).padStart(2,"0")}`),duration:t.duration,watched:o,quizCompleted:i,rewardReceived:l,quizResult:n,rewardAmount:l?.5:0})}var a;this.learningRecords.sort(((e,t)=>new Date(t.watchTime)-new Date(e.watchTime)))},viewUserDetail(e){s({url:`/pages/admin/users/info?userId=${e.id}`})},viewPromoter(e){s({url:`/pages/admin/users/info?promoterId=${e}`})},goBack(){r()},copyUsername(e){o({data:e,success:()=>{a({title:"用户名已复制",icon:"success",duration:1500})},fail:()=>{a({title:"复制失败",icon:"none",duration:1500})}})},copyUserId(){o({data:String(this.userInfo.id),success:()=>{a({title:"ID已复制",icon:"success",duration:1500})},fail:()=>{a({title:"复制失败",icon:"none",duration:1500})}})},formatDate(e){if(!e)return"";const t=new Date(e);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`},formatDateTime(e){if(!e)return"";const t=new Date(e);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")} ${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`}}},[["render",function(e,t,a,s,r,o){const U=u,R=x,b=i(l("u-tag"),m),C=i(l("u-icon"),f),z=i(l("u-button"),h),$=n("TimeFilter"),j=i(l("u-empty"),p),B=S;return g(),d(U,{class:"container"},{default:c((()=>[I(U,{class:"fixed-user-section"},{default:c((()=>[r.userInfo?(g(),d(U,{key:0,class:"custom-card"},{default:c((()=>[I(U,{class:"user-header"},{default:c((()=>[I(U,{class:"user-avatar-section"},{default:c((()=>[r.userInfo.avatar?(g(),v("img",{key:0,src:e.buildCompleteFileUrl(r.userInfo.avatar),style:{width:"60px",height:"60px","border-radius":"8px","object-fit":"cover"}},null,8,["src"])):(g(),d(U,{key:1,class:"avatar-placeholder",style:{width:"60px",height:"60px","border-radius":"8px","background-color":"#f0f9ff",color:"#186BFF",display:"flex","align-items":"center","justify-content":"center","font-size":"24px","font-weight":"bold"}},{default:c((()=>{var e,t;return[_(y((null==(t=null==(e=r.userInfo.username)?void 0:e.charAt(0))?void 0:t.toUpperCase())||"U"),1)]})),_:1}))])),_:1}),I(U,{class:"user-basic-info"},{default:c((()=>[I(U,{class:"user-name-row"},{default:c((()=>[I(R,{class:"user-name",onClick:t[0]||(t[0]=w((e=>o.copyUsername(r.userInfo.username)),["stop"]))},{default:c((()=>[_(y(r.userInfo.username||r.userInfo.nickname||"未设置"),1)])),_:1}),I(b,{text:"promoter"===r.userInfo.type?"推广员":"用户",type:"promoter"===r.userInfo.type?"warning":"info",shape:"circle",size:"mini"},null,8,["text","type"])])),_:1}),I(U,{class:"user-id"},{default:c((()=>[I(R,{class:"id-label"},{default:c((()=>[_("ID: ")])),_:1}),I(R,{class:"id-value"},{default:c((()=>[_(y(r.userInfo.id),1)])),_:1}),I(z,{type:"primary",size:"mini",shape:"circle",onClick:w(o.copyUserId,["stop"]),customStyle:{marginLeft:"16rpx"}},{default:c((()=>[I(C,{name:"copy",size:"12",color:"#fff"}),I(R,{style:{"margin-left":"8rpx","font-size":"20rpx"}},{default:c((()=>[_("复制")])),_:1})])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1}),I(U,{class:"user-details"},{default:c((()=>[I(U,{class:"detail-grid"},{default:c((()=>[I(U,{class:"detail-item"},{default:c((()=>[I(C,{name:"calendar",size:"20",color:"#186BFF"}),I(U,{class:"detail-content"},{default:c((()=>[I(R,{class:"detail-label"},{default:c((()=>[_("注册时间")])),_:1}),I(R,{class:"detail-value"},{default:c((()=>[_(y(o.formatDate(r.userInfo.registerTime)),1)])),_:1})])),_:1})])),_:1}),I(U,{class:"detail-item"},{default:c((()=>[I(C,{name:"clock",size:"20",color:"#186BFF"}),I(U,{class:"detail-content"},{default:c((()=>[I(R,{class:"detail-label"},{default:c((()=>[_("最后登录")])),_:1}),I(R,{class:"detail-value"},{default:c((()=>[_(y(o.formatDate(r.userInfo.lastLoginTime)||"从未登录"),1)])),_:1})])),_:1})])),_:1}),r.userInfo.employeeId?(g(),d(U,{key:0,class:"detail-item"},{default:c((()=>[I(C,{name:"account",size:"20",color:"#186BFF"}),I(U,{class:"detail-content"},{default:c((()=>[I(R,{class:"detail-label"},{default:c((()=>[_("员工ID")])),_:1}),I(R,{class:"detail-value"},{default:c((()=>[_(y(r.userInfo.employeeId),1)])),_:1})])),_:1})])),_:1})):D("",!0),"promoter"===r.userInfo.type?(g(),d(U,{key:1,class:"detail-item"},{default:c((()=>[I(C,{name:"account-fill",size:"20",color:"#186BFF"}),I(U,{class:"detail-content"},{default:c((()=>[I(R,{class:"detail-label"},{default:c((()=>[_("推广用户")])),_:1}),I(R,{class:"detail-value"},{default:c((()=>[_(y(r.relatedUsers.length)+"人",1)])),_:1})])),_:1})])),_:1})):(g(),d(U,{key:2,class:"detail-item"},{default:c((()=>[I(C,{name:"play-circle",size:"20",color:"#186BFF"}),I(U,{class:"detail-content"},{default:c((()=>[I(R,{class:"detail-label"},{default:c((()=>[_("观看视频")])),_:1}),I(R,{class:"detail-value"},{default:c((()=>[_(y(r.userInfo.watchedVideos||0)+"个",1)])),_:1})])),_:1})])),_:1}))])),_:1})])),_:1}),"promoter"===r.userInfo.type?(g(),d($,{key:0,modelValue:r.activeTimeFilter,"onUpdate:modelValue":t[1]||(t[1]=e=>r.activeTimeFilter=e),onChange:o.handleTimeFilterChange},null,8,["modelValue","onChange"])):D("",!0)])),_:1})):D("",!0)])),_:1}),I(B,{class:"scrollable-content","scroll-y":"true"},{default:c((()=>[r.userInfo&&"promoter"!==r.userInfo.type?(g(),d(U,{key:0,class:"learning-records"},{default:c((()=>[I(U,{class:"section-title"},{default:c((()=>[_("学习记录 ("+y(r.learningRecords.length)+")",1)])),_:1}),I(U,{class:"record-list"},{default:c((()=>[(g(!0),v(F,null,T(r.learningRecords,((t,a)=>(g(),d(U,{class:"learning-item",key:a},{default:c((()=>[I(U,{class:"video-info"},{default:c((()=>[I(U,{class:"video-cover",style:k({backgroundImage:`url(${t.cover&&!t.cover.startsWith("/static")?e.buildCompleteFileUrl(t.cover):t.cover})`})},null,8,["style"]),I(U,{class:"video-details"},{default:c((()=>[I(U,{class:"video-title"},{default:c((()=>[_(y(t.title),1)])),_:2},1024),I(U,{class:"video-time"},{default:c((()=>[_(y(t.watchTime),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),I(U,{class:"activity-details"},{default:c((()=>[I(U,{class:"detail-item"},{default:c((()=>[I(R,{class:"detail-text"},{default:c((()=>[_("观看进度")])),_:1}),I(R,{class:"detail-value"},{default:c((()=>[_(y(t.duration),1)])),_:2},1024)])),_:2},1024),I(U,{class:"detail-item"},{default:c((()=>[I(R,{class:"detail-text"},{default:c((()=>[_("答题结果")])),_:1}),I(R,{class:"detail-value"},{default:c((()=>[_(y(t.quizCompleted?t.quizResult?"答对":"答错":"未答题"),1)])),_:2},1024)])),_:2},1024),I(U,{class:"detail-item"},{default:c((()=>[I(R,{class:"detail-text"},{default:c((()=>[_("获得奖励")])),_:1}),I(R,{class:"detail-value"},{default:c((()=>[_(y(t.rewardReceived?t.rewardAmount+"元":"0.00元"),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128)),0===r.learningRecords.length?(g(),d(j,{key:0,mode:"data",icon:"inbox",text:"暂无学习记录",iconSize:80,textSize:28,iconColor:"#c8c9cc",textColor:"#909399"})):D("",!0)])),_:1})])),_:1})):D("",!0)])),_:1})])),_:1})}],["__scopeId","data-v-f61ab9b3"]]);export{V as default};
