/**
 * 测试用户数据服务
 * 用于在测试环境中模拟不同用户登录状态
 */

// 测试用户数据（基于提供的SQL数据）
const TEST_USERS = [
  {
    id: '1',
    openId: 'oNHwxjgrzgL2xpmM4GqmAcMaXXX1',
    unionId: 'oNHwxjgrzgL2xpmM4GqmAcMaUUU1',
    nickname: '小明同学',
    avatar: '/images/avatar-placeholder.png',
    employeeId: 'emp_001',
    auditStatus: 1,
    lastLogin: '2025-07-22 07:45:00',
    creatorName: '王小明'
  },
  {
    id: '2',
    openId: 'oNHwxjgrzgL2xpmM4GqmAcMaXXX2',
    unionId: 'oNHwxjgrzgL2xpmM4GqmAcMaUUU2',
    nickname: '阳光女孩',
    avatar: '/images/avatar-placeholder.png',
    employeeId: 'emp_001',
    auditStatus: 1,
    lastLogin: '2025-07-22 05:30:00',
    creatorName: '王小明'
  },
  {
    id: '3',
    openId: 'oNHwxjgrzgL2xpmM4GqmAcMaXXX3',
    unionId: 'oNHwxjgrzgL2xpmM4GqmAcMaUUU3',
    nickname: '健康达人',
    avatar: '/images/avatar-placeholder.png',
    employeeId: 'emp_002',
    auditStatus: 1,
    lastLogin: '2025-07-22 22:15:00',
    creatorName: '刘小红'
  },
  {
    id: '4',
    openId: 'oNHwxjgrzgL2xpmM4GqmAcMaXXX4',
    unionId: 'oNHwxjgrzgL2xpmM4GqmAcMaUUU4',
    nickname: '学习小能手',
    avatar: '/images/avatar-placeholder.png',
    employeeId: 'emp_002',
    auditStatus: 1,
    lastLogin: '2025-07-22 17:40:00',
    creatorName: '刘小红'
  },
  {
    id: '5',
    openId: 'oNHwxjgrzgL2xpmM4GqmAcMaXXX5',
    unionId: 'oNHwxjgrzgL2xpmM4GqmAcMaUUU5',
    nickname: '运动爱好者',
    avatar: '/images/avatar-placeholder.png',
    employeeId: 'emp_003',
    auditStatus: 1,
    lastLogin: '2025-07-22 20:50:00',
    creatorName: '赵小强'
  },
  {
    id: '9',
    openId: 'openid_001',
    unionId: 'unionid_001',
    nickname: '张小明',
    avatar: '/images/avatar-placeholder.png',
    employeeId: 'emp_001',
    auditStatus: 1,
    lastLogin: '2025-07-16 11:24:56',
    creatorName: '王员工'
  },
  {
    id: '10',
    openId: 'openid_002',
    unionId: 'unionid_002',
    nickname: '李小红',
    avatar: '/images/avatar-placeholder.png',
    employeeId: 'emp_001',
    auditStatus: 1,
    lastLogin: '2025-07-15 11:24:56',
    creatorName: '王员工'
  },
  {
    id: '11',
    openId: 'openid_003',
    unionId: 'unionid_003',
    nickname: '王小刚',
    avatar: '/images/avatar-placeholder.png',
    employeeId: 'emp_001',
    auditStatus: 1,
    lastLogin: '2025-07-17 10:24:56',
    creatorName: '王员工'
  },
  {
    id: '12',
    openId: 'openid_004',
    unionId: 'unionid_004',
    nickname: '赵小美',
    avatar: '/images/avatar-placeholder.png',
    employeeId: 'emp_001',
    auditStatus: 1,
    lastLogin: '2025-07-14 11:24:56',
    creatorName: '王员工'
  },
  {
    id: '13',
    openId: 'openid_005',
    unionId: 'unionid_005',
    nickname: '刘小强',
    avatar: '/images/avatar-placeholder.png',
    employeeId: 'emp_001',
    auditStatus: 1,
    lastLogin: '2025-07-17 06:24:56',
    creatorName: '王员工'
  }
];

/**
 * 测试用户服务类
 */
class TestUserService {
  /**
   * 获取所有测试用户
   * @returns {Array} 测试用户列表
   */
  getAllTestUsers() {
    return TEST_USERS;
  }

  /**
   * 根据ID获取测试用户
   * @param {string} userId - 用户ID
   * @returns {Object|null} 用户信息
   */
  getTestUserById(userId) {
    return TEST_USERS.find(user => user.id === userId) || null;
  }

  /**
   * 模拟用户登录
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 登录结果
   */
  async simulateUserLogin(userId) {
    try {
      const user = this.getTestUserById(userId);
      if (!user) {
        throw new Error('测试用户不存在');
      }

      // 生成模拟token
      const mockToken = `test_token_${userId}_${Date.now()}`;

      // 格式化用户信息（与真实登录保持一致）
      const userInfo = {
        id: user.id,
        openId: user.openId,
        unionId: user.unionId,
        nickname: user.nickname,
        avatar: user.avatar,
        employeeId: user.employeeId,
        auditStatus: user.auditStatus,
        lastLogin: user.lastLogin,
        creatorName: user.creatorName
      };

      // 保存到本地存储（使用与真实登录相同的键名）
      uni.setStorageSync('wechatUserInfo', userInfo);
      uni.setStorageSync('wechatUserToken', mockToken);

      // 标记为测试登录状态
      uni.setStorageSync('isTestLogin', true);
      uni.setStorageSync('testUserId', userId);

      console.log('测试用户登录成功:', userInfo);

      return {
        success: true,
        message: '测试登录成功',
        data: {
          userInfo: userInfo,
          token: mockToken
        }
      };
    } catch (error) {
      console.error('测试用户登录失败:', error);
      return {
        success: false,
        message: error.message || '测试登录失败'
      };
    }
  }

  /**
   * 清除测试登录状态
   */
  clearTestLogin() {
    try {
      uni.removeStorageSync('wechatUserInfo');
      uni.removeStorageSync('wechatUserToken');
      uni.removeStorageSync('isTestLogin');
      uni.removeStorageSync('testUserId');
      
      console.log('测试登录状态已清除');
      return true;
    } catch (error) {
      console.error('清除测试登录状态失败:', error);
      return false;
    }
  }

  /**
   * 检查是否为测试登录状态
   * @returns {boolean}
   */
  isTestLogin() {
    try {
      return uni.getStorageSync('isTestLogin') === true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取当前测试用户ID
   * @returns {string|null}
   */
  getCurrentTestUserId() {
    try {
      return uni.getStorageSync('testUserId') || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * 检查是否为测试环境
   * @returns {boolean}
   */
  isTestEnvironment() {
    try {
      // 检查配置中的调试模式
      if (window.APP_CONFIG && window.APP_CONFIG.debugMode) {
        return true;
      }
      
      // 检查URL是否包含localhost或测试域名
      const hostname = window.location.hostname;
      return hostname === 'localhost' || hostname.includes('test') || hostname.includes('dev');
    } catch (error) {
      return false;
    }
  }
}

// 创建单例实例
const testUserService = new TestUserService();

export default testUserService;
