<template>
    <view class="container">
        <!-- 页面头部 -->
        <u-navbar title="设置" :autoBack="true" :placeholder="true"></u-navbar>

        <!-- 用户审核设置 -->
        <view class="settings-section">
            <view class="setting-card">
                <view class="setting-header">
                    <text class="setting-title">用户审核</text>
                </view>
                <view class="setting-content">
                    <view class="setting-info">
                        <text class="info-main">开启后，新用户注册需要人工审核</text>
                        <text class="info-sub">关闭后，新用户将自动通过审核</text>
                    </view>
                    <view class="setting-control">
                        <view class="custom-switch" :class="{ 'switch-on': enableUserAudit }" @click="toggleSwitch">
                            <view class="switch-handle"></view>
                        </view>
                        <text class="switch-text">{{ enableUserAudit ? '开启' : '关闭' }}</text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 影响范围说明 -->
        <view class="impact-section">
            <u-notice-bar text="配置变更将立即生效，影响您下属的所有员工。请谨慎操作。" type="warning" :scrollable="false">
            </u-notice-bar>
        </view>

        <!-- 加载状态 -->
        <u-loadmore v-if="loading" status="loading" loadingText="加载中..." />
    </view>
</template>

<script>
import { getManagerAuditConfig, setManagerAuditConfig } from "@/api/sysuser.js";
import adminAuthService from "@/utils/adminAuthService.js";
import permissionMixin from '@/mixins/permission-mixin.js';

export default {
    name: 'AdminSettings',
    mixins: [permissionMixin],
    data () {
        return {
            loading: false,
            enableUserAudit: false, // 是否开启用户审核
            switchLoading: false,   // 开关加载状态
            managerId: ''           // 当前管理员ID
        }
    },
    onLoad () {
        // 检查权限 - 管理员和超管都能访问设置页面
        const userType = adminAuthService.getUserType();
        if (!['manager', 'agent', 'admin', 'super_admin'].includes(userType)) {
            uni.showModal({
                title: '权限不足',
                content: '只有管理员和超管可以访问设置页面',
                showCancel: false,
                success: () => {
                    uni.navigateBack();
                }
            });
            return;
        }
        this.initManagerInfo();
        this.loadAuditConfig();
    },
    methods: {
        initManagerInfo () {
            try {
                // 获取当前管理员信息
                const loginInfo = adminAuthService.getLoginInfo();
                this.managerId = loginInfo.id || loginInfo.userId || loginInfo.user_id || '';

                console.log('当前管理员信息:', loginInfo);
                console.log('管理员ID:', this.managerId);

            } catch (error) {
                console.error('获取管理员信息失败:', error);
                this.$u.toast('获取管理员信息失败');
                uni.navigateBack();
            }
        },

        async loadAuditConfig () {
            try {
                this.loading = true;

                // 调用真实API获取当前管理员的审核配置
                console.log('开始获取审核配置...');
                const response = await getManagerAuditConfig();

                console.log('获取审核配置响应:', response);

                if (response.success) {
                    this.enableUserAudit = response.data || false;
                    console.log('审核配置加载成功:', this.enableUserAudit);
                } else {
                    console.error('获取审核配置失败:', response.msg);
                    this.$u.toast(response.msg || '获取配置失败');
                    this.enableUserAudit = false; // 默认关闭
                }

            } catch (error) {
                console.error('获取审核配置失败:', error);
                this.$u.toast('获取配置失败');
                this.enableUserAudit = false; // 默认关闭
            } finally {
                this.loading = false;
            }
        },

        toggleSwitch () {
            if (this.switchLoading) return;

            // 计算新的状态值，但不立即改变，等API成功后再改变
            const newValue = !this.enableUserAudit;
            this.handleUserAuditChange(newValue);
        },

        async handleUserAuditChange (newValue) {
            try {
                this.switchLoading = true;

                // 如果没有传入新值，使用当前值（用于页面初始化时的调用）
                const targetValue = newValue !== undefined ? newValue : this.enableUserAudit;

                // 调用真实API更新管理员的审核配置
                console.log('开始更新审核配置:', targetValue);
                const response = await setManagerAuditConfig(targetValue);

                console.log('更新审核配置响应:', response);

                if (response.success) {
                    // API成功，更新状态
                    this.enableUserAudit = targetValue;
                    const statusText = targetValue ? '开启' : '关闭';
                    this.$u.toast(`已${statusText}用户审核功能`);
                    console.log('审核配置更新成功:', targetValue);
                } else {
                    console.error('更新审核配置失败:', response.msg);
                    // API失败，不改变状态，显示错误信息
                    this.$u.toast(response.msg || '更新失败，请稍后重试');
                }

            } catch (error) {
                console.error('更新审核配置失败:', error);
                // 网络错误，不改变状态，显示错误信息
                this.$u.toast('网络错误，请稍后重试');
            } finally {
                this.switchLoading = false;
            }
        }
    }
}
</script>

<style lang="scss">
.container {
    min-height: 100vh;
    background-color: #f5f5f5;
}

.settings-section {
    padding: 20rpx 16rpx 0 16rpx;
}

.impact-section {
    margin: 20rpx 16rpx;
}

.setting-card {
    background-color: #ffffff;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

.setting-header {
    padding: 24rpx;
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
}

.setting-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #303133;
    line-height: 1.4;
}

.setting-content {
    padding: 24rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20rpx;
}

.setting-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8rpx;
}

.info-main {
    font-size: 28rpx;
    color: #303133;
    line-height: 1.5;
}

.info-sub {
    font-size: 24rpx;
    color: #909399;
    line-height: 1.5;
}

.setting-control {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: 12rpx;
}

.custom-switch {
    width: 100rpx;
    height: 60rpx;
    background-color: #dcdfe6;
    border-radius: 30rpx;
    position: relative;
    transition: all 0.3s ease;
    cursor: pointer;
}

.custom-switch.switch-on {
    background-color: #409eff;
}

.switch-handle {
    width: 52rpx;
    height: 52rpx;
    background-color: #ffffff;
    border-radius: 50%;
    position: absolute;
    top: 4rpx;
    left: 4rpx;
    transition: all 0.3s ease;
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.switch-on .switch-handle {
    left: 44rpx;
}

.switch-text {
    font-size: 24rpx;
    color: #606266;
    font-weight: 500;
}

/* 美化卡片样式 */
:deep(.u-card) {
    border-radius: 12rpx !important;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06) !important;
    border: none !important;
    overflow: hidden;
}

/* 美化开关 */
:deep(.u-switch) {
    transform: scale(0.9);
}
</style>
