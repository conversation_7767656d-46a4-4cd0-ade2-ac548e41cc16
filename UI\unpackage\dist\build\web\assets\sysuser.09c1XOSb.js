import{m as s}from"./index-BbEPSs4u.js";function t(t){return s.post("/SysUser/create",t)}function r(t){return s.get(`/SysUser/${t}`)}function e(t){return s.post("/SysUser/change-password",t)}function n(t){return s.post("/SysUser/reset-password",t)}function o(t={}){return s.get("/SysUser/administrators",t)}function a(t={}){return s.get("/SysUser/employees",t)}function u(t,r={}){return s.get(`/SysUser/subordinates/${t}`,r)}function i(t,r){return e={userId:t,status:r},s.post("/SysUser/update",e);var e}function c(){return s.get("/SysUser/audit-config")}function f(t){const r=JSON.stringify(t);return s.post("/SysUser/audit-config",r,{headers:{"Content-Type":"application/json"}})}export{u as a,o as b,e as c,a as d,t as e,c as f,r as g,n as r,f as s,i as t};
