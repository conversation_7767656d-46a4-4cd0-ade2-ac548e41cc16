import{_ as e,I as t,r as a,a9 as s,b as i,X as l,Y as r,Z as n,$ as o,e as u,f as d,av as c,aa as p,o as m,c as f,w as h,A as y,B as g,F as x,k as v,i as k,h as _,j as L,t as w,m as $,V as b,x as j,aF as A}from"./index-BbEPSs4u.js";import{m as D,b as F}from"./media-common.CwXDcy84.js";import{p as z}from"./permission-mixin.0SnNiQ4H.js";function C(e,t){return $.post(`/UserAudit/audit-user/${e}`,t)}const T=e({mixins:[z],components:{AuditList:e({name:"AuditList",mixins:[D],props:{auditType:{type:String,required:!0},listData:{type:Array,required:!0},extraFields:{type:Array,default:()=>[]},typeLabel:{type:String,required:!0},loading:{type:Boolean,default:!1}},data:()=>({}),methods:{getStatusText:e=>"approved"===e?"已通过":"rejected"===e?"已拒绝":"待审核",getStatusType:e=>"approved"===e?"success":"rejected"===e?"error":"warning",formatDate:e=>F(e),showConfirmModal(e,a){t("approve"===a?{title:"确认通过",content:"确定要通过该申请吗？",success:t=>{t.confirm&&this.$emit("approve",e)}}:{title:"确认拒绝",content:"确定要拒绝该申请吗？",success:t=>{t.confirm&&this.$emit("reject",{item:e,reason:"拒绝"})}})},viewDetail(e){this.$emit("view-detail",e)},getRoleText:e=>({employee:"员工",agent:"代理",user:"用户"}[e]||"未知"),getFieldValue(e,t){if(t.path&&"string"==typeof t.path){const a=t.path.split(".");let s=e;for(const e of a){if(null==s)return t.defaultValue||"无";s=s[e]}return s||t.defaultValue||"无"}return t.defaultValue||"无"}}},[["render",function(e,t,$,b,j,A){const D=a(i("u-loadmore"),s),F=a(i("u-avatar"),l),z=k,C=a(i("u-tag"),r),T=a(i("u-cell"),n),S=a(i("u-cell-group"),o),V=a(i("u-icon"),u),I=a(i("u-button"),d),R=a(i("u-card"),c),U=a(i("u-empty"),p);return m(),f(z,{class:"audit-container"},{default:h((()=>[$.loading?(m(),f(D,{key:0,status:"loading",loadingText:"加载中..."})):(m(),f(z,{key:1,class:"audit-list"},{default:h((()=>[(m(!0),y(x,null,g($.listData,((t,a)=>(m(),f(R,{key:t.id,padding:0,margin:"0 0 20rpx 0"},{head:h((()=>[_(z,{class:"card-header"},{default:h((()=>[_(z,{class:"user-basic-info"},{default:h((()=>[_(F,{src:e.buildCompleteFileUrl(t.avatar)||"/static/images/avatar-placeholder.png",size:"40",shape:"square"},null,8,["src"]),_(z,{class:"user-info"},{default:h((()=>[_(z,{class:"user-name"},{default:h((()=>[L(w(t.nickname||t.username||t.name||"未知用户"),1)])),_:2},1024),_(C,{text:A.getRoleText(t.role||$.auditType),type:"info",size:"mini",plain:!0},null,8,["text"])])),_:2},1024)])),_:2},1024),_(C,{text:A.getStatusText(t.status),type:A.getStatusType(t.status),size:"mini"},null,8,["text","type"])])),_:2},1024)])),body:h((()=>[_(z,{class:"card-body"},{default:h((()=>[$.extraFields.length>0?(m(),f(S,{key:0,border:!1},{default:h((()=>[(m(!0),y(x,null,g($.extraFields,((e,a)=>(m(),f(T,{key:a,title:e.label,value:A.getFieldValue(t,e),border:!1,size:"small"},null,8,["title","value"])))),128))])),_:2},1024)):v("",!0)])),_:2},1024)])),foot:h((()=>[_(z,{class:"card-footer"},{default:h((()=>["pending"===t.status?(m(),y(x,{key:0},[_(I,{text:"拒绝",type:"info",plain:!0,size:"small",onClick:e=>A.showConfirmModal(t,"reject")},{icon:h((()=>[_(V,{name:"close",size:"14"})])),_:2},1032,["onClick"]),_(I,{text:"通过",type:"primary",size:"small",onClick:e=>A.showConfirmModal(t,"approve")},{icon:h((()=>[_(V,{name:"checkmark",size:"14"})])),_:2},1032,["onClick"])],64)):(m(),f(I,{key:1,text:"查看详情",type:"info",plain:!0,size:"small",onClick:e=>A.viewDetail(t)},{icon:h((()=>[_(V,{name:"eye",size:"14"})])),_:2},1032,["onClick"]))])),_:2},1024)])),_:2},1024)))),128)),0===$.listData.length?(m(),f(U,{key:0,mode:"data",text:`暂无${$.typeLabel}申请`},null,8,["text"])):v("",!0)])),_:1}))])),_:1})}],["__scopeId","data-v-5667d632"]])},data:()=>({userList:[],loading:!1,extraFields:[]}),onLoad(){this.loadUserList()},methods:{async loadUserList(){try{this.loading=!0;const e=await $.get("/UserAudit/pending-users");e.success&&e.data?this.userList=(e.data||[]).map((e=>({...e,status:this.convertStatus(e.status||0)}))):(this.$u.toast(e.msg||"获取用户列表失败"),this.userList=[])}catch(e){console.error("获取待审核用户列表失败:",e),this.$u.toast("网络错误，请稍后重试"),this.userList=[]}finally{this.loading=!1}},convertStatus:e=>({0:"pending",1:"approved",2:"rejected"}[e]||"pending"),async handleReject(e){try{const{item:t,reason:a}=e,s=await function(e,t=""){return C(e,{status:2,remark:t})}(t.id,a||"无");if(s.success){const e=this.userList.findIndex((e=>e.id===t.id));-1!==e&&this.userList.splice(e,1),this.$u.toast("已拒绝申请")}else this.$u.toast(s.msg||"操作失败")}catch(t){console.error("拒绝审核出错:",t),this.$u.toast("网络错误，请稍后重试")}},async handleApprove(e){try{const t=await function(e,t=""){return C(e,{status:1,remark:t})}(e.id);if(t.success){const t=this.userList.findIndex((t=>t.id===e.id));-1!==t&&this.userList.splice(t,1),this.$u.toast("已通过申请")}else this.$u.toast(t.msg||"操作失败")}catch(t){console.error("通过审核出错:",t),this.$u.toast("网络错误，请稍后重试")}},viewDetail(e){b({url:`/pages/admin/users/info?userId=${e.id}&type=user`})}}},[["render",function(e,t,s,l,r,n){const o=a(i("u-navbar"),A),u=j("AuditList"),d=k;return m(),f(d,{class:"container"},{default:h((()=>[_(o,{title:"用户审核",autoBack:!0,placeholder:!0}),_(u,{auditType:"user",listData:r.userList,extraFields:r.extraFields,typeLabel:"用户",loading:r.loading,onReject:n.handleReject,onApprove:n.handleApprove,onViewDetail:n.viewDetail},null,8,["listData","extraFields","loading","onReject","onApprove","onViewDetail"])])),_:1})}],["__scopeId","data-v-9f7d98c3"]]);export{T as default};
