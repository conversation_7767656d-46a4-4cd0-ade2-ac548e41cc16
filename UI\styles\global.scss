/* 全局通用样式 */
@import './variables.scss';

/* === 页面基础样式 === */
page {
  background-color: $bg-secondary;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: $font-size-base;
  line-height: $line-height-normal;
  color: $text-primary;

  /* 修复页面布局问题 */
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

/* === uni-app 修复样式 === */
/* 确保底部导航栏在合适的层级，但不影响弹出层 */
.uni-tabbar,
.uni-tabbar-bottom,
.uni-tabbar__content,
uni-tabbar,
.tab-bar,
.tabbar,
.tab-bar-container,
.tab-bar-wrapper,
.uni-app__tabbar {
  z-index: 1000 !important;
}

/* 修复uni-app页面头部问题 */
.uni-page-head,
.uni-page-head-bd,
.uni-page-head-ft {
  height: auto !important;
  min-height: 0 !important;
}

/* === 全宽布局容器 === */
.page-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: $bg-tertiary;
}

/* 修复自定义导航栏页面的内容区域 */
.container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-top: 0 !important;
}

/* 确保页面内容紧贴顶部 */
.page-wrapper {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

/* === 固定头部布局 === */
.fixed-header-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: $bg-secondary;
}

.page-header {
  position: relative;
  z-index: $z-index-sticky;
  flex-shrink: 0;
  background-color: $bg-primary;
  box-shadow: $shadow-sm;
}

.page-header-custom {
  display: flex;
  align-items: center;
  justify-content: center;
  height: $header-height;
  padding-top: var(--status-bar-height, 0);
  background-color: $bg-primary;
  position: relative;
}

.page-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-primary;
}

.scrollable-content {
  flex: 1;
  height: 0;
  overflow-y: auto;
  background-color: $bg-secondary;
}

/* === 全宽内容区域 === */
.content-section {
  width: 100%;
  background-color: $bg-primary;
  margin-bottom: $spacing-base;
}

.section-header {
  padding: $spacing-lg $page-padding;
  border-bottom: 1rpx solid $border-secondary;
  background-color: $bg-primary;
}

.section-title {
  font-size: $font-size-md;
  font-weight: $font-weight-semibold;
  color: $text-primary;
  margin: 0;
}

.section-content {
  padding: $spacing-lg $page-padding;
}

/* === 列表样式 === */
// .list-container {
//   background-color: $bg-primary;
// }

.list-item {
  padding: $spacing-lg $page-padding;
  border-bottom: 1rpx solid $border-secondary;
  background-color: $bg-primary;
  transition: background-color $transition-fast ease;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
  background-color: $bg-tertiary;
}

/* === 搜索和筛选区域 === */
.search-filter-section {
  background-color: $bg-primary;
  padding: $spacing-lg $page-padding;
  border-bottom: 1rpx solid $border-secondary;
}

/* === 统一搜索框样式 === */
.search-box {
  padding: 16rpx 20rpx;
  background-color: $bg-primary;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-input {
  display: flex;
  align-items: center;
  background-color: $bg-tertiary;
  border-radius: 40rpx;
  padding: 0rpx 24rpx;
  flex: 1;
  border: 1rpx solid $border-secondary;
  transition: all 0.3s;
}

.search-input:focus-within {
  border-color: $primary-color;
  box-shadow: 0 0 8rpx rgba(24, 107, 255, 0.2);
}

.search-input .search-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.search-input input {
  flex: 1;
  height: 60rpx;
  font-size: $font-size-base;
  background: transparent;
  border: none;
  outline: none;
  color: $text-primary;
}

.search-input input::placeholder {
  color: $text-tertiary;
}

.clear-icon {
  color: $text-secondary;
  font-size: 40rpx;
  line-height: 1;
}

/* === 通用弹窗样式 === */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: $bg-mask;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: $z-index-modal;
}

.modal-content {
  width: 80%;
  background-color: $bg-primary;
  border-radius: $border-radius-md;
  overflow: hidden;
  box-shadow: $shadow-lg;
}

.modal-header {
  padding: $spacing-xl;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid $border-secondary;
}

.modal-title {
  font-size: $font-size-md;
  font-weight: $font-weight-semibold;
  color: $text-primary;
}

.close-btn {
  font-size: $font-size-xl;
  color: $text-tertiary;
  cursor: pointer;
  transition: color $transition-fast ease;
}

.close-btn:hover {
  color: $text-secondary;
}

.modal-body {
  padding: $spacing-xl;
}

.modal-text {
  font-size: $font-size-base;
  color: $text-primary;
  margin-bottom: $spacing-xl;
  text-align: center;
  line-height: $line-height-relaxed;
}

.modal-btns {
  display: flex;
  justify-content: space-between;
  gap: $spacing-md;
}

.modal-btn {
  flex: 1;
  padding: $spacing-base 0;
  font-size: $font-size-base;
  border-radius: $border-radius-sm;
  border: none;
  cursor: pointer;
  transition: all $transition-base ease;
  font-weight: $font-weight-medium;
}

.modal-btn.cancel {
  background-color: $bg-tertiary;
  color: $text-primary;
}

.modal-btn.cancel:active {
  background-color: $gray-300;
}

.modal-btn.confirm {
  background-color: $primary-color;
  color: $text-white;
}

.modal-btn.confirm:active {
  background-color: $primary-dark;
}

.modal-btn[disabled] {
  background-color: $bg-disabled;
  color: $text-disabled;
  cursor: not-allowed;
}

/* === 表单样式 === */
.form-item {
  margin-bottom: $spacing-xl;
  position: relative;
}

.form-label {
  display: block;
  font-size: $font-size-base;
  color: $text-primary;
  margin-bottom: $spacing-base;
  font-weight: $font-weight-medium;
}

.form-input {
  width: 100%;
  height: $input-height;
  padding: 0 $input-padding;
  border: 2rpx solid $border-primary;
  border-radius: $border-radius-base;
  font-size: $font-size-md;
  box-sizing: border-box;
  background-color: $bg-secondary;
  transition: all $transition-base ease;
  outline: none;
  line-height: calc(#{$input-height} - 4rpx);
  color: $text-primary;
}

.form-input:focus {
  border-color: $primary-color;
  background-color: $bg-primary;
  box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.1);
}

.form-input::placeholder {
  color: $text-tertiary;
  font-size: $font-size-base;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: $spacing-lg;
  border: 2rpx solid $border-primary;
  border-radius: $border-radius-base;
  font-size: $font-size-base;
  box-sizing: border-box;
  background-color: $bg-secondary;
  transition: all $transition-base ease;
  outline: none;
  color: $text-primary;
  resize: none;
  line-height: $line-height-relaxed;
}

.form-textarea:focus {
  border-color: $primary-color;
  background-color: $bg-primary;
  box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.1);
}

/* 卡片样式已移至 components.scss */

/* === 按钮样式 === */
.btn {
  padding: $spacing-base $button-padding;
  border-radius: $border-radius-sm;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  border: none;
  cursor: pointer;
  transition: all $transition-base ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: $button-height;
  box-sizing: border-box;
}

.btn-primary {
  background-color: $primary-color;
  color: $text-white;
}

.btn-primary:active {
  background-color: $primary-dark;
  transform: scale(0.98);
}

.btn-secondary {
  background-color: $secondary-color;
  color: $text-white;
}

.btn-secondary:active {
  background-color: darken($secondary-color, 10%);
  transform: scale(0.98);
}

.btn-success {
  background-color: $success-color;
  color: $text-white;
}

.btn-success:active {
  background-color: darken($success-color, 10%);
  transform: scale(0.98);
}

.btn-danger {
  background-color: $error-color;
  color: $text-white;
}

.btn-danger:active {
  background-color: darken($error-color, 10%);
  transform: scale(0.98);
}

.btn-outline {
  background-color: transparent;
  border: 2rpx solid $primary-color;
  color: $primary-color;
}

.btn-outline:active {
  background-color: $primary-color;
  color: $text-white;
}

.btn-text {
  background-color: transparent;
  color: $primary-color;
  // padding: $spacing-sm $spacing-lg;
}

.btn-text:active {
  background-color: rgba(24, 107, 255, 0.1);
}

.btn-small {
  height: 60rpx;
  padding: $spacing-xs $spacing-lg;
  font-size: $font-size-sm;
}

.btn-large {
  height: 100rpx;
  padding: $spacing-lg $spacing-xxl;
  font-size: $font-size-md;
}

.btn[disabled] {
  background-color: $bg-disabled;
  color: $text-disabled;
  cursor: not-allowed;
  transform: none;
}

/* === 底部弹出层样式 === */
.action-sheet-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8rpx);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.action-sheet {
  width: 100%;
  background: #fff;
  border-radius: 10rpx 10rpx 0 0;
  overflow: hidden;
  animation: slideUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.15);
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }

  to {
    transform: translateY(0);
  }
}

.action-sheet-header {
  padding: $spacing-xxxl $spacing-xl $spacing-lg;
  text-align: center;
  background: linear-gradient(135deg, $primary-light 0%, $primary-dark 100%);
  color: $text-white;
  position: relative;
}

.action-sheet-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3rpx;
}

.action-sheet-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #fff;
  display: block;
  margin-bottom: 12rpx;
}

.action-sheet-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  display: block;
  font-weight: 500;
}

.action-sheet-content {
  padding: 16rpx 0;
}

.action-sheet-item {
  display: flex;
  align-items: center;
  padding: 32rpx 32rpx;
  margin: 0 16rpx 8rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  background: #fff;
}

.action-sheet-item:last-child {
  margin-bottom: 16rpx;
}

.action-sheet-item:active {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: #fff;
  transform: scale(0.98);
}

.action-sheet-item.danger {
  color: #e17055;
}

.action-sheet-item.danger:active {
  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
  color: #fff;
}

.action-sheet-icon {
  font-size: 36rpx;
  margin-right: 24rpx;
  width: 48rpx;
  text-align: center;
}

.action-sheet-text {
  font-size: 32rpx;
  flex: 1;
  font-weight: 500;
}

.action-sheet-cancel {
  padding: 24rpx 32rpx 48rpx;
  text-align: center;
  background: #f8f9fa;
  border-top: 16rpx solid #e9ecef;
  cursor: pointer;
}

.action-sheet-cancel-text {
  font-size: 32rpx;
  color: #6c757d;
  font-weight: 600;
  padding: 16rpx 32rpx;
  background: #fff;
  border-radius: 50rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.action-sheet-cancel:active .action-sheet-cancel-text {
  background: #e9ecef;
  transform: scale(0.98);
}

/* === 工具类 === */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}



/* === 悬浮按钮样式 === */
.floating-btn-wrapper {
  position: fixed;
  z-index: 999;
}

.floating-add-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #007AFF, #0056CC);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(0, 122, 255, 0.4);
  position: relative;
  transition: all 0.3s ease;
}

.floating-add-btn .iconfont {
  color: #fff;
  font-size: 32rpx;
  margin-bottom: 2rpx;
}

.floating-add-btn .btn-text {
  color: #fff;
  font-size: 18rpx;
  font-weight: 500;
}

.floating-add-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
}