/**
 * 观看记录相关API
 * 提供观看记录的查询、统计等功能
 */

import request from '@/utils/request.js'

/**
 * 获取批次观看记录
 * @param {string|number} batchId - 批次ID
 * @param {Object} params - 查询参数
 * @param {number} params.pageIndex - 页码
 * @param {number} params.pageSize - 每页数量
 * @returns {Promise<Object>} 观看记录列表
 */
export function getBatchViewRecords (batchId, params = {}) {
  return request.get(`/api/batch/${batchId}/view-records`, params)
}

/**
 * 获取观看统计数据
 * @param {Object} params - 查询参数
 * @param {string|number} params.batchId - 批次ID
 * @returns {Promise<Object>} 观看统计数据
 */
export function getViewStatistics (params = {}) {
  return request.get('/api/view-statistics', params)
}

/**
 * 获取用户观看记录
 * @param {string|number} userId - 用户ID
 * @param {string|number} batchId - 批次ID
 * @returns {Promise<Object>} 用户观看记录
 */
export function getUserViewRecord (userId, batchId) {
  return request.get(`/api/user/${userId}/batch/${batchId}/view-record`)
}

/**
 * 创建或更新观看记录
 * @param {Object} data - 观看记录数据
 * @param {string|number} data.userId - 用户ID
 * @param {string|number} data.batchId - 批次ID
 * @param {number} data.watchProgress - 观看进度
 * @param {number} data.viewDuration - 观看时长
 * @returns {Promise<Object>} 创建结果
 */
export function createOrUpdateViewRecord (data) {
  return request.post('/api/view-record', data)
}

/**
 * 更新观看进度
 * @param {string|number} recordId - 记录ID
 * @param {Object} data - 更新数据
 * @param {number} data.watchProgress - 观看进度
 * @param {number} data.viewDuration - 观看时长
 * @param {boolean} data.isCompleted - 是否完成
 * @returns {Promise<Object>} 更新结果
 */
export function updateWatchProgress (recordId, data) {
  return request.put(`/api/view-record/${recordId}/progress`, data)
}

// 默认导出所有API函数
export default {
  getBatchViewRecords,
  getViewStatistics,
  getUserViewRecord,
  createOrUpdateViewRecord,
  updateWatchProgress
}
